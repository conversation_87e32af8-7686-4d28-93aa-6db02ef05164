#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows环境最小化测试
"""

import torch
import numpy as np

def test_basic():
    """基础测试"""
    print("=" * 50)
    print("Windows环境基础测试")
    print("=" * 50)
    
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU数量: {torch.cuda.device_count()}")
    
    # 测试基础张量操作
    x = torch.randn(100, 64)
    y = torch.randn(64, 32)
    z = torch.mm(x, y)
    print(f"✅ 基础张量运算正常: {z.shape}")
    
    # 测试稀疏张量
    indices = torch.LongTensor([[0, 1, 1], [2, 0, 2]])
    values = torch.FloatTensor([3, 4, 5])
    shape = (2, 3)
    sparse_tensor = torch.sparse_coo_tensor(indices, values, shape)
    print(f"✅ 稀疏张量创建正常: {sparse_tensor.shape}")
    
    return True

def test_model_components():
    """测试模型组件"""
    print("\n🧪 测试模型组件...")
    
    try:
        # 测试嵌入层
        embedding = torch.nn.Embedding(1000, 64)
        input_ids = torch.LongTensor([1, 2, 3, 4, 5])
        output = embedding(input_ids)
        print(f"✅ 嵌入层正常: {output.shape}")
        
        # 测试线性层
        linear = torch.nn.Linear(64, 32)
        output2 = linear(output)
        print(f"✅ 线性层正常: {output2.shape}")
        
        # 测试损失函数
        pos_scores = torch.randn(10)
        neg_scores = torch.randn(10)
        bpr_loss = -torch.mean(torch.log(torch.sigmoid(pos_scores - neg_scores) + 1e-8))
        print(f"✅ BPR损失计算正常: {bpr_loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型组件测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始Windows环境最小化测试...")
    
    success = True
    
    # 基础测试
    if not test_basic():
        success = False
    
    # 模型组件测试
    if not test_model_components():
        success = False
    
    if success:
        print(f"\n🎉 Windows环境测试通过！")
        print(f"💡 建议在Linux服务器上运行完整训练")
        print(f"🚀 Linux命令: bash train_simple.sh")
    else:
        print(f"\n💥 Windows环境测试失败")
    
    return success

if __name__ == "__main__":
    main()
