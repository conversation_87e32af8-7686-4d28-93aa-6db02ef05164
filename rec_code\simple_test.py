#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试脚本
"""

import os
import sys
import torch
import numpy as np

def test_data_loading():
    """测试数据加载"""
    print("=== 测试数据加载 ===")
    try:
        from mooccube_dataloader import MOOCCubeDataset
        
        dataset = MOOCCubeDataset(
            path="../data/MOOCCube",
            min_interactions=5,
            cache_dir="test_cache"
        )
        
        print(f"✓ 数据加载成功")
        print(f"  用户数: {dataset.n_users}")
        print(f"  视频数: {dataset.m_items}")
        print(f"  训练交互数: {dataset.trainDataSize}")
        print(f"  测试交互数: {dataset.testDataSize}")
        
        return dataset
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        return None

def test_model_creation(dataset):
    """测试模型创建"""
    print("\n=== 测试模型创建 ===")
    try:
        from multi_intent_model import MultiIntentVideoRecommender
        
        # 创建模拟嵌入
        video_intent_embeddings = {}
        user_intent_embeddings = {}
        
        # 只为前10个视频和用户创建嵌入
        for i in range(min(10, dataset.m_items)):
            video_id = dataset.idx_to_video.get(i, f"video_{i}")
            video_intent_embeddings[video_id] = np.random.randn(384).tolist()
        
        for i in range(min(10, dataset.n_users)):
            user_intent_embeddings[i] = np.random.randn(384).tolist()
        
        config = {
            'latent_dim_rec': 64,
            'intent_dim': 384,
            'hidden_dim': 512,
            'final_dim': 64,
            'lightGCN_n_layers': 3,
            'dropout': 0,
            'A_split': False,
            'A_n_fold': 100,
            'temperature': 0.1
        }
        
        model = MultiIntentVideoRecommender(
            config=config,
            dataset=dataset,
            video_intent_embeddings=video_intent_embeddings,
            user_intent_embeddings=user_intent_embeddings
        )
        
        print(f"✓ 模型创建成功")
        print(f"  参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        return model
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_forward_pass(model, dataset):
    """测试前向传播"""
    print("\n=== 测试前向传播 ===")
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        
        batch_size = 4
        users = torch.randint(0, dataset.n_users, (batch_size,)).to(device)
        pos_items = torch.randint(0, dataset.m_items, (batch_size,)).to(device)
        neg_items = torch.randint(0, dataset.m_items, (batch_size,)).to(device)
        
        with torch.no_grad():
            users_emb, pos_emb, neg_emb, contrastive_loss = model(users, pos_items, neg_items)
            
        print(f"✓ 前向传播成功")
        print(f"  用户嵌入形状: {users_emb.shape}")
        print(f"  正样本嵌入形状: {pos_emb.shape}")
        print(f"  负样本嵌入形状: {neg_emb.shape}")
        print(f"  对比损失: {contrastive_loss.item():.4f}")
        print(f"  设备: {device}")
        
        return True
        
    except Exception as e:
        print(f"✗ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("多重意图视频推荐系统 - 简化测试")
    print("=" * 50)
    
    # 创建测试目录
    os.makedirs("test_cache", exist_ok=True)
    
    # 测试数据加载
    dataset = test_data_loading()
    if dataset is None:
        print("\n❌ 数据加载失败，无法继续测试")
        return False
    
    # 测试模型创建
    model = test_model_creation(dataset)
    if model is None:
        print("\n❌ 模型创建失败，无法继续测试")
        return False
    
    # 测试前向传播
    forward_success = test_forward_pass(model, dataset)
    if not forward_success:
        print("\n❌ 前向传播失败")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！")
    print("系统已准备好进行训练")
    
    # 清理测试缓存
    import shutil
    if os.path.exists("test_cache"):
        shutil.rmtree("test_cache")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
