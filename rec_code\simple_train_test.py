#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的训练测试脚本
"""

import os
import sys
import torch
import numpy as np
import time

def test_model_creation():
    """测试模型创建"""
    print("🔧 测试模型创建...")
    
    try:
        from multi_intent_model import MultiIntentVideoRecommender
        
        # 模拟配置
        config = {
            'latent_dim_rec': 64,
            'intent_dim': 384,
            'hidden_dim': 512,
            'final_dim': 64,
            'lightGCN_n_layers': 3,
            'dropout': 0.0,
            'A_split': False,
            'pretrain': 0
        }
        
        # 创建模型
        model = MultiIntentVideoRecommender(
            num_users=1000,
            num_items=500,
            config=config
        )
        
        print(f"✅ 模型创建成功")
        print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试前向传播
        user_ids = torch.randint(0, 1000, (32,))
        pos_items = torch.randint(0, 500, (32,))
        neg_items = torch.randint(0, 500, (32,))
        
        loss = model.bpr_loss(user_ids, pos_items, neg_items)
        print(f"✅ 前向传播测试成功，损失: {loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_and_model():
    """测试数据加载和模型训练"""
    print("=" * 60)
    print("MOOCCube 多重意图视频推荐系统 - 简化测试")
    print("=" * 60)
    
    # 1. 测试数据加载
    print("\n📊 步骤1: 测试数据加载")
    try:
        from mooccube_dataloader import MOOCCubeDataset
        
        dataset = MOOCCubeDataset(
            path="../data/MOOCCube",
            min_interactions=5,
            cache_dir="simple_test_cache"
        )
        
        print(f"✅ 数据加载成功")
        print(f"   用户数: {dataset.n_users:,}")
        print(f"   视频数: {dataset.m_items:,}")
        print(f"   训练交互: {dataset.trainDataSize:,}")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False
    
    # 2. 测试模型创建
    print("\n🤖 步骤2: 测试模型创建")
    if not test_model_creation():
        return False
    
    # 3. 测试训练一个epoch
    print("\n🏃 步骤3: 测试训练一个epoch")
    try:
        from multi_intent_model import MultiIntentVideoRecommender
        
        config = {
            'latent_dim_rec': 64,
            'intent_dim': 384,
            'hidden_dim': 512,
            'final_dim': 64,
            'lightGCN_n_layers': 3,
            'dropout': 0.0,
            'A_split': False,
            'pretrain': 0,
            'bpr_weight': 1.0,
            'contrastive_weight': 0.1,
            'temperature': 0.1
        }
        
        model = MultiIntentVideoRecommender(
            num_users=dataset.n_users,
            num_items=dataset.m_items,
            config=config
        )
        
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        # 训练一个小批次
        model.train()
        users, pos_items, neg_items = dataset.sample()
        
        # 转换为tensor
        users = torch.LongTensor(users)
        pos_items = torch.LongTensor(pos_items)
        neg_items = torch.LongTensor(neg_items)
        
        optimizer.zero_grad()
        loss = model.bpr_loss(users, pos_items, neg_items)
        loss.backward()
        optimizer.step()
        
        print(f"✅ 训练测试成功，损失: {loss.item():.4f}")
        
    except Exception as e:
        print(f"❌ 训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print(f"\n🎉 所有测试通过！系统工作正常")
    return True

def main():
    """主函数"""
    try:
        success = test_data_and_model()
        return success
    finally:
        # 清理缓存
        import shutil
        if os.path.exists("simple_test_cache"):
            try:
                shutil.rmtree("simple_test_cache")
            except:
                pass

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✨ 简化测试完成，系统准备就绪！")
        print("💡 现在可以运行完整训练: python train_multi_intent.py --dataset mooccube --path ../data/MOOCCube")
    else:
        print("\n💥 测试失败，请检查配置")
    
    sys.exit(0 if success else 1)
