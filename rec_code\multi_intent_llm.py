import json
import os
import time
import pickle
from typing import Dict, List, Tuple
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class MultiIntentLLMProcessor:
    """
    多重意图LLM处理器，用于理解视频和用户的多重意图
    """
    
    def __init__(self, api_key: str = None, cache_dir: str = "cache", batch_size: int = 10):
        self.api_key = api_key
        self.cache_dir = cache_dir
        self.batch_size = batch_size
        os.makedirs(cache_dir, exist_ok=True)
        
        # 线程锁，用于API调用限制
        self.api_lock = threading.Lock()
        self.api_call_count = 0
        self.last_api_time = time.time()
        
        # 提示词模板
        self.video_prompt_template = """
你是一位在线教育专家，请从多重意图角度分析以下教学视频：

视频信息：{video_text}

请从以下多重意图维度进行分析：
1. **知识获取意图**: 该视频主要传授什么知识点，适合什么学习目标
2. **技能提升意图**: 观看该视频能提升哪些实践技能或应用能力  
3. **考试准备意图**: 该视频对应哪些考试要点或评估标准
4. **兴趣探索意图**: 该视频能激发学习者对哪些领域的兴趣
5. **职业发展意图**: 该视频内容对哪些职业方向有帮助

请用连贯的段落描述该视频的多重教育价值和适用意图，不超过200字。
"""

        self.user_prompt_template = """
你是一位学习行为分析专家，请分析用户的多重学习意图：

用户学习历史：
{user_text}

请从以下多重意图维度分析用户偏好：
1. **知识深度偏好**: 用户偏向基础概念学习还是深度专业知识
2. **技能应用偏好**: 用户更关注理论学习还是实践技能
3. **学习目标偏好**: 用户学习是为了考试、工作还是兴趣
4. **学科领域偏好**: 用户在哪些知识领域表现出持续兴趣
5. **学习节奏偏好**: 用户的学习连续性和深度探索特点

请总结用户的多重学习意图画像，不超过150字。
"""
    
    def _rate_limit_api_call(self):
        """API调用速率限制"""
        with self.api_lock:
            current_time = time.time()
            if current_time - self.last_api_time < 1.0:  # 每秒最多1次调用
                time.sleep(1.0 - (current_time - self.last_api_time))
            self.last_api_time = time.time()
            self.api_call_count += 1
    
    def _call_llm_api(self, prompt: str, max_retries: int = 3) -> str:
        """
        调用LLM API
        这里使用模拟的API调用，实际使用时需要替换为真实的API
        """
        self._rate_limit_api_call()
        
        # 模拟API调用延迟
        time.sleep(0.1)
        
        # 这里应该是真实的API调用
        # 例如调用OpenAI GPT、Claude、或本地LLM
        
        # 模拟返回结果
        if "视频信息" in prompt:
            return self._generate_mock_video_response(prompt)
        else:
            return self._generate_mock_user_response(prompt)
    
    def _generate_mock_video_response(self, prompt: str) -> str:
        """生成模拟的视频意图分析结果"""
        responses = [
            "该视频主要面向知识获取意图，系统讲解了核心概念和理论基础，适合初学者建立知识框架。在技能提升方面，通过实例演示帮助学习者掌握实际应用方法。对于考试准备，覆盖了重要考点和解题思路。同时激发了学习者对相关领域的兴趣探索，为后续深入学习奠定基础。在职业发展方面，所学内容与行业实践紧密结合，有助于提升专业竞争力。",
            
            "该视频重点关注技能提升意图，通过项目实战和案例分析，帮助学习者掌握实用技能。知识获取方面涵盖了必要的理论支撑，为技能应用提供基础。考试准备维度包含了实践类题目的解答策略。兴趣探索方面展示了技术应用的广阔前景，激发学习热情。职业发展角度突出了技能在工作中的直接应用价值，有助于职场能力提升。",
            
            "该视频主要服务于考试准备意图，系统梳理了考试重点和难点，提供了高效的复习策略。知识获取方面强化了核心概念的理解和记忆。技能提升通过解题技巧训练提高应试能力。兴趣探索方面通过知识点的深度解析激发学习兴趣。职业发展角度体现在通过考试认证提升职业资质和竞争力。"
        ]
        
        import random
        return random.choice(responses)
    
    def _generate_mock_user_response(self, prompt: str) -> str:
        """生成模拟的用户意图分析结果"""
        responses = [
            "该用户表现出强烈的知识深度偏好，倾向于系统性学习和深度探索。技能应用方面更注重理论与实践的结合。学习目标主要面向职业发展，希望通过学习提升专业能力。学科领域偏好集中在技术类和管理类课程。学习节奏呈现持续性特点，具有良好的学习规划和执行能力。",
            
            "该用户偏向实用性学习，注重技能提升和实际应用。知识深度方面更关注核心要点而非全面覆盖。学习目标兼顾考试准备和职业发展。学科领域偏好较为广泛，显示出较强的学习适应性。学习节奏相对灵活，根据需要调整学习强度和深度。",
            
            "该用户展现出兴趣驱动的学习特点，知识深度偏好适中，更注重学习的广度和多样性。技能应用方面偏重理论学习。学习目标主要为兴趣探索和知识拓展。学科领域偏好呈现多元化特征。学习节奏较为随意，根据兴趣点进行深入学习。"
        ]
        
        import random
        return random.choice(responses)
    
    def process_video_intents(self, video_texts: Dict[str, str]) -> Dict[str, str]:
        """
        批量处理视频多重意图理解
        
        Args:
            video_texts: {video_id: video_text_description}
            
        Returns:
            {video_id: intent_analysis}
        """
        cache_file = os.path.join(self.cache_dir, "video_intent_responses.json")
        
        # 加载缓存
        if os.path.exists(cache_file):
            print("Loading video intent responses from cache...")
            with open(cache_file, 'r', encoding='utf-8') as f:
                cached_responses = json.load(f)
        else:
            cached_responses = {}
        
        # 找出需要处理的视频
        missing_videos = [vid for vid in video_texts.keys() if vid not in cached_responses]
        
        if not missing_videos:
            print("All video intents already cached.")
            return {vid: cached_responses[vid] for vid in video_texts.keys() if vid in cached_responses}
        
        print(f"Processing intents for {len(missing_videos)} videos...")
        
        # 批量处理
        for i in range(0, len(missing_videos), self.batch_size):
            batch_videos = missing_videos[i:i + self.batch_size]
            print(f"Processing batch {i//self.batch_size + 1}/{(len(missing_videos)-1)//self.batch_size + 1}")
            
            # 并行处理批次
            with ThreadPoolExecutor(max_workers=3) as executor:
                future_to_video = {}
                
                for video_id in batch_videos:
                    video_text = video_texts[video_id]
                    prompt = self.video_prompt_template.format(video_text=video_text)
                    future = executor.submit(self._call_llm_api, prompt)
                    future_to_video[future] = video_id
                
                # 收集结果
                for future in as_completed(future_to_video):
                    video_id = future_to_video[future]
                    try:
                        response = future.result()
                        cached_responses[video_id] = response
                        print(f"Processed video {video_id}")
                    except Exception as e:
                        print(f"Error processing video {video_id}: {e}")
                        cached_responses[video_id] = "处理失败"
            
            # 每个批次后保存缓存
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cached_responses, f, ensure_ascii=False, indent=2)
            
            # 批次间休息
            time.sleep(2)
        
        print(f"Video intent processing completed. Total API calls: {self.api_call_count}")
        return {vid: cached_responses[vid] for vid in video_texts.keys() if vid in cached_responses}
    
    def process_user_intents(self, user_texts: Dict[int, str]) -> Dict[int, str]:
        """
        批量处理用户多重意图理解
        
        Args:
            user_texts: {user_idx: user_text_description}
            
        Returns:
            {user_idx: intent_analysis}
        """
        cache_file = os.path.join(self.cache_dir, "user_intent_responses.json")
        
        # 加载缓存
        if os.path.exists(cache_file):
            print("Loading user intent responses from cache...")
            with open(cache_file, 'r', encoding='utf-8') as f:
                cached_responses = json.load(f)
                # 转换键为整数
                cached_responses = {int(k): v for k, v in cached_responses.items()}
        else:
            cached_responses = {}
        
        # 找出需要处理的用户
        missing_users = [uid for uid in user_texts.keys() if uid not in cached_responses]
        
        if not missing_users:
            print("All user intents already cached.")
            return {uid: cached_responses[uid] for uid in user_texts.keys() if uid in cached_responses}
        
        print(f"Processing intents for {len(missing_users)} users...")
        
        # 批量处理
        for i in range(0, len(missing_users), self.batch_size):
            batch_users = missing_users[i:i + self.batch_size]
            print(f"Processing batch {i//self.batch_size + 1}/{(len(missing_users)-1)//self.batch_size + 1}")
            
            # 并行处理批次
            with ThreadPoolExecutor(max_workers=3) as executor:
                future_to_user = {}
                
                for user_idx in batch_users:
                    user_text = user_texts[user_idx]
                    prompt = self.user_prompt_template.format(user_text=user_text)
                    future = executor.submit(self._call_llm_api, prompt)
                    future_to_user[future] = user_idx
                
                # 收集结果
                for future in as_completed(future_to_user):
                    user_idx = future_to_user[future]
                    try:
                        response = future.result()
                        cached_responses[user_idx] = response
                        print(f"Processed user {user_idx}")
                    except Exception as e:
                        print(f"Error processing user {user_idx}: {e}")
                        cached_responses[user_idx] = "处理失败"
            
            # 每个批次后保存缓存
            with open(cache_file, 'w', encoding='utf-8') as f:
                # 转换键为字符串以便JSON序列化
                cache_to_save = {str(k): v for k, v in cached_responses.items()}
                json.dump(cache_to_save, f, ensure_ascii=False, indent=2)
            
            # 批次间休息
            time.sleep(2)
        
        print(f"User intent processing completed. Total API calls: {self.api_call_count}")
        return {uid: cached_responses[uid] for uid in user_texts.keys() if uid in cached_responses}
    
    def get_cached_responses(self) -> Tuple[Dict[str, str], Dict[int, str]]:
        """获取所有缓存的响应"""
        video_cache_file = os.path.join(self.cache_dir, "video_intent_responses.json")
        user_cache_file = os.path.join(self.cache_dir, "user_intent_responses.json")
        
        video_responses = {}
        user_responses = {}
        
        if os.path.exists(video_cache_file):
            with open(video_cache_file, 'r', encoding='utf-8') as f:
                video_responses = json.load(f)
        
        if os.path.exists(user_cache_file):
            with open(user_cache_file, 'r', encoding='utf-8') as f:
                user_responses_raw = json.load(f)
                user_responses = {int(k): v for k, v in user_responses_raw.items()}
        
        return video_responses, user_responses
    
    def estimate_api_cost(self, num_videos: int, num_users: int, cost_per_call: float = 0.01) -> float:
        """估算API调用成本"""
        total_calls = num_videos + num_users
        estimated_cost = total_calls * cost_per_call
        print(f"Estimated API cost: ${estimated_cost:.2f} for {total_calls} calls")
        return estimated_cost


class IntentEmbeddingProcessor:
    """
    意图嵌入处理器，将LLM生成的意图文本转换为向量表示
    """

    def __init__(self, model_name: str = "sentence-transformers/all-MiniLM-L6-v2", cache_dir: str = "cache"):
        self.model_name = model_name
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)

        # 初始化嵌入模型
        try:
            from sentence_transformers import SentenceTransformer
            self.embedding_model = SentenceTransformer(model_name)
            print(f"Loaded embedding model: {model_name}")
        except ImportError:
            print("Warning: sentence-transformers not installed. Using mock embeddings.")
            self.embedding_model = None

    def _generate_mock_embedding(self, text: str, dim: int = 384) -> List[float]:
        """生成模拟嵌入向量"""
        import hashlib
        import numpy as np

        # 使用文本哈希生成确定性的随机向量
        hash_obj = hashlib.md5(text.encode())
        seed = int(hash_obj.hexdigest()[:8], 16)
        np.random.seed(seed)

        # 生成单位向量
        vector = np.random.normal(0, 1, dim)
        vector = vector / np.linalg.norm(vector)
        return vector.tolist()

    def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """将文本编码为嵌入向量"""
        if self.embedding_model is not None:
            embeddings = self.embedding_model.encode(texts)
            return embeddings.tolist()
        else:
            # 使用模拟嵌入
            return [self._generate_mock_embedding(text) for text in texts]

    def process_video_intent_embeddings(self, video_intent_texts: Dict[str, str]) -> Dict[str, List[float]]:
        """处理视频意图嵌入"""
        cache_file = os.path.join(self.cache_dir, "video_intent_embeddings.pkl")

        if os.path.exists(cache_file):
            print("Loading video intent embeddings from cache...")
            with open(cache_file, 'rb') as f:
                cached_embeddings = pickle.load(f)

            missing_videos = [vid for vid in video_intent_texts.keys() if vid not in cached_embeddings]
            if not missing_videos:
                return {vid: cached_embeddings[vid] for vid in video_intent_texts.keys() if vid in cached_embeddings}
        else:
            cached_embeddings = {}
            missing_videos = list(video_intent_texts.keys())

        if missing_videos:
            print(f"Generating embeddings for {len(missing_videos)} video intents...")
            missing_texts = [video_intent_texts[vid] for vid in missing_videos]
            embeddings = self.encode_texts(missing_texts)

            for vid, emb in zip(missing_videos, embeddings):
                cached_embeddings[vid] = emb

            # 保存缓存
            with open(cache_file, 'wb') as f:
                pickle.dump(cached_embeddings, f)
            print("Video intent embeddings cached.")

        return {vid: cached_embeddings[vid] for vid in video_intent_texts.keys() if vid in cached_embeddings}

    def process_user_intent_embeddings(self, user_intent_texts: Dict[int, str]) -> Dict[int, List[float]]:
        """处理用户意图嵌入"""
        cache_file = os.path.join(self.cache_dir, "user_intent_embeddings.pkl")

        if os.path.exists(cache_file):
            print("Loading user intent embeddings from cache...")
            with open(cache_file, 'rb') as f:
                cached_embeddings = pickle.load(f)

            missing_users = [uid for uid in user_intent_texts.keys() if uid not in cached_embeddings]
            if not missing_users:
                return {uid: cached_embeddings[uid] for uid in user_intent_texts.keys() if uid in cached_embeddings}
        else:
            cached_embeddings = {}
            missing_users = list(user_intent_texts.keys())

        if missing_users:
            print(f"Generating embeddings for {len(missing_users)} user intents...")
            missing_texts = [user_intent_texts[uid] for uid in missing_users]
            embeddings = self.encode_texts(missing_texts)

            for uid, emb in zip(missing_users, embeddings):
                cached_embeddings[uid] = emb

            # 保存缓存
            with open(cache_file, 'wb') as f:
                pickle.dump(cached_embeddings, f)
            print("User intent embeddings cached.")

        return {uid: cached_embeddings[uid] for uid in user_intent_texts.keys() if uid in cached_embeddings}
