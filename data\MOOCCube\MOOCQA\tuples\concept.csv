id,name
K_B树_计算机科学技术,B树
K_IP地址_计算机科学技术,IP地址
K_KMP算法_计算机科学技术,KMP算法
K_ML语言_计算机科学技术,ML语言
K_S完备化_计算机科学技术,S完备化
K_t分布_数学,t分布
K_一元函数_数学,一元函数
K_上升沿_计算机科学技术,上升沿
K_上界_数学,上界
K_下界_数学,下界
K_下降方向_数学,下降方向
K_下降法_数学,下降法
K_下降算法_数学,下降算法
K_不确定性推理_计算机科学技术,不确定性推理
K_不等式_数学,不等式
K_不等式约束_数学,不等式约束
K_专家系统_计算机科学技术,专家系统
K_中位数_数学,中位数
K_中序遍历_计算机科学技术,中序遍历
K_中断_计算机科学技术,中断
K_中断处理_计算机科学技术,中断处理
K_中断机制_计算机科学技术,中断机制
K_中断源_计算机科学技术,中断源
K_中间件_计算机科学技术,中间件
K_串匹配_计算机科学技术,串匹配
K_临界值_数学,临界值
K_主动学习_计算机科学技术,主动学习
K_主成分分析_数学,主成分分析
K_事件检测_计算机科学技术,事件检测
K_二元运算_数学,二元运算
K_二分搜索_计算机科学技术,二分搜索
K_二叉查找树_计算机科学技术,二叉查找树
K_二叉树_计算机科学技术,二叉树
K_二路归并_计算机科学技术,二路归并
K_二进制_数学,二进制
K_二项分布_数学,二项分布
K_云计算_计算机科学技术,云计算
K_互信息_数学,互信息
K_互熵_数学,互熵
K_互连网络_计算机科学技术,互连网络
K_交互式学习_计算机科学技术,交互式学习
K_交叉熵_计算机科学技术,交叉熵
K_交换式以太网_计算机科学技术,交换式以太网
K_交换律_数学,交换律
K_交换排序_计算机科学技术,交换排序
K_人工神经网络_计算机科学技术,人工神经网络
K_代码优化_计算机科学技术,代码优化
K_以太网_计算机科学技术,以太网
K_仿射函数_数学,仿射函数
K_优先队列_计算机科学技术,优先队列
K_传输层_计算机科学技术,传输层
K_传递函数_数学,传递函数
K_传递性_数学,传递性
K_伪随机数_数学,伪随机数
K_似然_数学,似然
K_似然函数_数学,似然函数
K_信号处理_计算机科学技术,信号处理
K_信噪比_数学,信噪比
K_信息流_计算机科学技术,信息流
K_信息熵_数学,信息熵
K_信息系统_计算机科学技术,信息系统
K_信息隐蔽_计算机科学技术,信息隐蔽
K_信标帧_计算机科学技术,信标帧
K_信道容量_数学,信道容量
K_值域_数学,值域
K_假值_数学,假值
K_T_假设检验_数学,假设检验
K_偏序_数学,偏序
K_停机问题_数学,停机问题
K_偶数_数学,偶数
K_傅里叶变换_数学,傅里叶变换
K_兄弟_计算机科学技术,兄弟
K_充要条件_数学,充要条件
K_先进先出_计算机科学技术,先进先出
K_先验分布_数学,先验分布
K_先验概率_数学,先验概率
K_光流场_计算机科学技术,光流场
K_入侵防御系统_计算机科学技术,入侵防御系统
K_入度_计算机科学技术,入度
K_全局极大值_数学,全局极大值
K_八皇后问题_计算机科学技术,八皇后问题
K_公开密钥加密_计算机科学技术,公开密钥加密
K_共轭梯度法_数学,共轭梯度法
K_关联矩阵_数学,关联矩阵
K_内分_数学,内分
K_内排序_计算机科学技术,内排序
K_内核_计算机科学技术,内核
K_内积_数学,内积
K_内部中断_计算机科学技术,内部中断
K_冲突_计算机科学技术,冲突
K_冲突检测_计算机科学技术,冲突检测
K_冲突避免_计算机科学技术,冲突避免
K_决策树_数学,决策树
K_准线_数学,准线
K_减函数_数学,减函数
K_减法_数学,减法
K_几何分布_数学,几何分布
K_凸二次规划_数学,凸二次规划
K_凸函数_数学,凸函数
K_T_凸分析_数学,凸分析
K_凸规划_数学,凸规划
K_凸集_数学,凸集
K_函数_数学,函数
K_分区表_计算机科学技术,分区表
K_分布函数_数学,分布函数
K_分布式操作系统_计算机科学技术,分布式操作系统
K_分布式计算_计算机科学技术,分布式计算
K_分数_数学,分数
K_分治_计算机科学技术,分治
K_分离超平面_数学,分离超平面
K_分类_计算机科学技术,分类
K_分辨率_计算机科学技术,分辨率
K_划分_数学,划分
K_划分算法_计算机科学技术,划分算法
K_列向量_数学,列向量
K_列地址_计算机科学技术,列地址
K_初始化值_计算机科学技术,初始化值
K_判别式_数学,判别式
K_前馈网络_计算机科学技术,前馈网络
K_剪枝_计算机科学技术,剪枝
K_割点_数学,割点
K_加密_数学,加密
K_加法结合律_数学,加法结合律
K_动作函数_计算机科学技术,动作函数
K_动态绑定_计算机科学技术,动态绑定
K_T_动态规划_数学,动态规划
K_势函数_数学,势函数
K_协方差_数学,协方差
K_协议数据单元_计算机科学技术,协议数据单元
K_协议族_计算机科学技术,协议族
K_单元测试_计算机科学技术,单元测试
K_单向函数_计算机科学技术,单向函数
K_单向鉴别_计算机科学技术,单向鉴别
K_单继承_计算机科学技术,单继承
K_单臂路由器_计算机科学技术,单臂路由器
K_单链表_计算机科学技术,单链表
K_卡尔曼滤波_数学,卡尔曼滤波
K_即时通信_计算机科学技术,即时通信
K_卷积_数学,卷积
K_原子性_计算机科学技术,原子性
K_原子操作_计算机科学技术,原子操作
K_原点_数学,原点
K_T_参数估计_数学,参数估计
K_参数空间_数学,参数空间
K_双向链表_计算机科学技术,双向链表
K_双端队列_计算机科学技术,双端队列
K_双线性函数_数学,双线性函数
K_变分问题_数学,变分问题
K_可执行文件_计算机科学技术,可执行文件
K_可执行程序_计算机科学技术,可执行程序
K_可测函数_数学,可测函数
K_可行解_数学,可行解
K_可辨识性_数学,可辨识性
K_可达区域_数学,可达区域
K_叶节点_计算机科学技术,叶节点
K_同心圆_数学,同心圆
K_同构_数学,同构
K_同源_数学,同源
K_后序遍历_计算机科学技术,后序遍历
K_后继_数学,后继
K_后进先出_计算机科学技术,后进先出
K_后验分布_数学,后验分布
K_后验概率_数学,后验概率
K_向量_数学,向量
K_命令接口_计算机科学技术,命令接口
K_命名空间_计算机科学技术,命名空间
K_和校验_数学,和校验
K_哈希函数_计算机科学技术,哈希函数
K_哈希表_计算机科学技术,哈希表
K_回文_计算机科学技术,回文
K_回路_数学,回路
K_因数分解_数学,因数分解
K_因特网_计算机科学技术,因特网
K_图像分割_计算机科学技术,图像分割
K_图像分类_计算机科学技术,图像分类
K_图像处理_计算机科学技术,图像处理
K_图像数据_计算机科学技术,图像数据
K_图形学_计算机科学技术,图形学
K_图灵机_数学,图灵机
K_图的遍历_计算机科学技术,图的遍历
K_图解法_数学,图解法
K_T_图论_数学,图论
K_在线学习_计算机科学技术,在线学习
K_地址寄存器_计算机科学技术,地址寄存器
K_地址总线_计算机科学技术,地址总线
K_地址映射_计算机科学技术,地址映射
K_地址空间_计算机科学技术,地址空间
K_地址解析_计算机科学技术,地址解析
K_地址计算_计算机科学技术,地址计算
K_均值_数学,均值
K_均值向量_数学,均值向量
K_均匀分布_数学,均匀分布
K_均方误差_数学,均方误差
K_域名_计算机科学技术,域名
K_基本函数_数学,基本函数
K_基本输入输出系统_计算机科学技术,基本输入输出系统
K_堆排序_计算机科学技术,堆排序
K_增强现实_计算机科学技术,增强现实
K_增量_数学,增量
K_增量学习_计算机科学技术,增量学习
K_复线性空间_数学,复线性空间
K_外部中断_计算机科学技术,外部中断
K_多模光纤_计算机科学技术,多模光纤
K_多用户系统_计算机科学技术,多用户系统
K_多继承_计算机科学技术,多继承
K_多输入多输出_计算机科学技术,多输入多输出
K_多项式_数学,多项式
K_多项式方程_数学,多项式方程
K_多项式时间算法_数学,多项式时间算法
K_大于_数学,大于
K_大数据_计算机科学技术,大数据
K_失真_数学,失真
K_头文件_计算机科学技术,头文件
K_奇偶性_数学,奇偶性
K_奥卡姆剃刀_计算机科学技术,奥卡姆剃刀
K_子串_计算机科学技术,子串
K_子图_数学,子图
K_子系统_数学,子系统
K_子集_数学,子集
K_字符串_计算机科学技术,字符串
K_字长_数学,字长
K_存储分配_计算机科学技术,存储分配
K_存储网络_计算机科学技术,存储网络
K_存储过程_计算机科学技术,存储过程
K_存在约束_计算机科学技术,存在约束
K_完全二叉树_计算机科学技术,完全二叉树
K_完全图_数学,完全图
K_完全类_数学,完全类
K_实数域_数学,实数域
K_实时操作系统_计算机科学技术,实时操作系统
K_实根_数学,实根
K_实轴_数学,实轴
K_密度函数_数学,密度函数
K_密码学_数学,密码学
K_密码算法_计算机科学技术,密码算法
K_密钥长度_计算机科学技术,密钥长度
K_对合_数学,对合
K_对数函数_数学,对数函数
K_对称矩阵_数学,对称矩阵
K_寻址_计算机科学技术,寻址
K_寻址方式_计算机科学技术,寻址方式
K_尺规作图法_数学,尺规作图法
K_局部优化_计算机科学技术,局部优化
K_局部参数_数学,局部参数
K_局部极小值_数学,局部极小值
K_局部环_数学,局部环
K_层次遍历_计算机科学技术,层次遍历
K_嵌入式系统_计算机科学技术,嵌入式系统
K_工作流_计算机科学技术,工作流
K_巴拿赫空间_数学,巴拿赫空间
K_布尔表达式_计算机科学技术,布尔表达式
K_布尔运算_计算机科学技术,布尔运算
K_希尔伯特空间_数学,希尔伯特空间
K_平均查找长度_计算机科学技术,平均查找长度
K_平行四边形_数学,平行四边形
K_平衡分布_数学,平衡分布
K_平面_数学,平面
K_并发性_计算机科学技术,并发性
K_并行处理_计算机科学技术,并行处理
K_广域网_计算机科学技术,广域网
K_广度优先搜索_计算机科学技术,广度优先搜索
K_序列密码_计算机科学技术,序列密码
K_度量空间_数学,度量空间
K_开区间_数学,开区间
K_开源软件_计算机科学技术,开源软件
K_异常_计算机科学技术,异常
K_异或门_计算机科学技术,异或门
K_异步传输_计算机科学技术,异步传输
K_张量_数学,张量
K_强化学习_计算机科学技术,强化学习
K_循环模_数学,循环模
K_循环移位_计算机科学技术,循环移位
K_循环网络_计算机科学技术,循环网络
K_T_微分方程_数学,微分方程
K_快速以太网_计算机科学技术,快速以太网
K_快速排序_计算机科学技术,快速排序
K_扩展服务集_计算机科学技术,扩展服务集
K_扫描线算法_计算机科学技术,扫描线算法
K_批量_数学,批量
K_投影变换_计算机科学技术,投影变换
K_报文交换_计算机科学技术,报文交换
K_抽样_数学,抽样
K_抽象数据类型_计算机科学技术,抽象数据类型
K_抽象类_计算机科学技术,抽象类
K_拉格朗日乘子法_数学,拉格朗日乘子法
K_拓扑线性空间_数学,拓扑线性空间
K_拟合_数学,拟合
K_拟牛顿法_数学,拟牛顿法
K_指令寄存器_计算机科学技术,指令寄存器
K_指令流_计算机科学技术,指令流
K_指数函数_数学,指数函数
K_按序执行_计算机科学技术,按序执行
K_损失函数_数学,损失函数
K_排列_数学,排列
K_排序_计算机科学技术,排序
K_排序问题_数学,排序问题
K_推理方法_计算机科学技术,推理方法
K_插值_数学,插值
K_插入排序_计算机科学技术,插入排序
K_搜索引擎_计算机科学技术,搜索引擎
K_搜索方向_数学,搜索方向
K_操作数_计算机科学技术,操作数
K_操作符重载_计算机科学技术,操作符重载
K_操作系统_计算机科学技术,操作系统
K_操作语义_计算机科学技术,操作语义
K_T_数值分析_数学,数值分析
K_数字用户线接入复用器_计算机科学技术,数字用户线接入复用器
K_数字计算机_计算机科学技术,数字计算机
K_数字通信系统_数学,数字通信系统
K_数学归纳法_数学,数学归纳法
K_数据_计算机科学技术,数据
K_数据传送_计算机科学技术,数据传送
K_数据元素_计算机科学技术,数据元素
K_数据加密_数学,数据加密
K_数据加密标准_计算机科学技术,数据加密标准
K_数据包_计算机科学技术,数据包
K_数据压缩_数学,数据压缩
K_数据完整性_计算机科学技术,数据完整性
K_数据对象_计算机科学技术,数据对象
K_数据封装_计算机科学技术,数据封装
K_T_数据库_计算机科学技术,数据库
K_数据服务_计算机科学技术,数据服务
K_数据源_计算机科学技术,数据源
K_数据类型_计算机科学技术,数据类型
K_数据组织_计算机科学技术,数据组织
K_数据结构_数学,数据结构
K_数据网络_计算机科学技术,数据网络
K_数据转换_计算机科学技术,数据转换
K_数据通道_计算机科学技术,数据通道
K_数理逻辑_数学,数理逻辑
K_数组_计算机科学技术,数组
K_整除_数学,整除
K_文件分配_计算机科学技术,文件分配
K_方程_数学,方程
K_无偏估计_数学,无偏估计
K_无向图_数学,无向图
K_无监督学习_计算机科学技术,无监督学习
K_无穷小_数学,无穷小
K_无线局域网_计算机科学技术,无线局域网
K_时钟周期_计算机科学技术,时钟周期
K_时间尺度_数学,时间尺度
K_时间序列_数学,时间序列
K_智能体_计算机科学技术,智能体
K_曲线_数学,曲线
K_替换算法_计算机科学技术,替换算法
K_最优估计_计算机科学技术,最优估计
K_最优值_数学,最优值
K_最优性_数学,最优性
K_最优解_数学,最优解
K_最佳拟合_数学,最佳拟合
K_最大传输单元_计算机科学技术,最大传输单元
K_最大似然估计_数学,最大似然估计
K_最大值问题_计算机科学技术,最大值问题
K_最大后验_计算机科学技术,最大后验
K_最大熵原理_计算机科学技术,最大熵原理
K_最小二乘法_数学,最小二乘法
K_最小距离_数学,最小距离
K_最短路径_计算机科学技术,最短路径
K_最速下降法_数学,最速下降法
K_有向图_数学,有向图
K_有向弧_计算机科学技术,有向弧
K_有向无环图_计算机科学技术,有向无环图
K_有效位数_数学,有效位数
K_有效性_数学,有效性
K_有效数字_数学,有效数字
K_有根树_数学,有根树
K_服务器_计算机科学技术,服务器
K_服务数据单元_计算机科学技术,服务数据单元
K_本元_数学,本元
K_机器人_计算机科学技术,机器人
K_机器指令_计算机科学技术,机器指令
K_权向量_数学,权向量
K_条件分布_数学,条件分布
K_条件概率_数学,条件概率
K_条件熵_数学,条件熵
K_松弛变量_数学,松弛变量
K_极值点_数学,极值点
K_极大值_数学,极大值
K_极大点_数学,极大点
K_极小化_数学,极小化
K_柱面_数学,柱面
K_标准元_数学,标准元
K_标记语言_计算机科学技术,标记语言
K_标识符_计算机科学技术,标识符
K_标量化_数学,标量化
K_栈顶_计算机科学技术,栈顶
K_树搜索算法_数学,树搜索算法
K_树算法_数学,树算法
K_树表示_数学,树表示
K_样本分布_数学,样本分布
K_样本均值_数学,样本均值
K_样本点_数学,样本点
K_样本空间_数学,样本空间
K_核函数_数学,核函数
K_核方法_计算机科学技术,核方法
K_梯度方向_数学,梯度方向
K_梯形_数学,梯形
K_检索树_计算机科学技术,检索树
K_森林_计算机科学技术,森林
K_概率_数学,概率
K_概率分布_数学,概率分布
K_T_概率论_数学,概率论
K_模块化_计算机科学技术,模块化
K_模型拟合_数学,模型拟合
K_模式识别_计算机科学技术,模式识别
K_模拟信号_计算机科学技术,模拟信号
K_模拟退火算法_计算机科学技术,模拟退火算法
K_欠拟合_计算机科学技术,欠拟合
K_正交变换_数学,正交变换
K_正交坐标_数学,正交坐标
K_正交基_数学,正交基
K_正交码_数学,正交码
K_正定核_数学,正定核
K_正定矩阵_数学,正定矩阵
K_正弦_数学,正弦
K_正确性证明_计算机科学技术,正确性证明
K_死锁预防_计算机科学技术,死锁预防
K_比例_数学,比例
K_汇编语言_计算机科学技术,汇编语言
K_活动分区_计算机科学技术,活动分区
K_浮点数_数学,浮点数
K_消息鉴别码_计算机科学技术,消息鉴别码
K_深度优先搜索_计算机科学技术,深度优先搜索
K_深度学习_计算机科学技术,深度学习
K_游程编码_数学,游程编码
K_溢出_计算机科学技术,溢出
K_满二叉树_计算机科学技术,满二叉树
K_激活函数_计算机科学技术,激活函数
K_点云_计算机科学技术,点云
K_点估计_数学,点估计
K_特征函数_数学,特征函数
K_特征向量_数学,特征向量
K_特征提取_计算机科学技术,特征提取
K_状态估计_数学,状态估计
K_状态变量_数学,状态变量
K_状态向量_数学,状态向量
K_状态空间_数学,状态空间
K_独立性_数学,独立性
K_理性智能体_计算机科学技术,理性智能体
K_用户标识_计算机科学技术,用户标识
K_用户进程_计算机科学技术,用户进程
K_电力线通信_计算机科学技术,电力线通信
K_电磁辐射_计算机科学技术,电磁辐射
K_画家算法_计算机科学技术,画家算法
K_目标函数_数学,目标函数
K_目标约束_数学,目标约束
K_直径_数学,直径
K_相对地址_计算机科学技术,相对地址
K_相对熵_数学,相对熵
K_真前缀_计算机科学技术,真前缀
K_矩阵的元_数学,矩阵的元
K_矩阵的迹_数学,矩阵的迹
K_矩阵表示_数学,矩阵表示
K_码元_计算机科学技术,码元
K_硬件_计算机科学技术,硬件
K_硬件抽象层_计算机科学技术,硬件抽象层
K_确定性算法_计算机科学技术,确定性算法
K_离散空间_数学,离散空间
K_积分_数学,积分
K_程序设计_计算机科学技术,程序设计
K_稳定性_数学,稳定性
K_穷举搜索_计算机科学技术,穷举搜索
K_空间坐标_数学,空间坐标
K_等差数列_数学,等差数列
K_等待时间_数学,等待时间
K_T_算术_数学,算术
K_算法_数学,算法
K_类函数_数学,类函数
K_类结构_数学,类结构
K_系统中断_计算机科学技术,系统中断
K_系统实施_计算机科学技术,系统实施
K_系统实现_计算机科学技术,系统实现
K_系统环境_计算机科学技术,系统环境
K_累积误差_数学,累积误差
K_约束传播_计算机科学技术,约束传播
K_约束集_数学,约束集
K_级数求和_数学,级数求和
K_线性变换_数学,线性变换
K_线性回归_数学,线性回归
K_线性搜索_数学,线性搜索
K_线性方程_数学,线性方程
K_T_线性模型_数学,线性模型
K_线性相关_数学,线性相关
K_线性空间_数学,线性空间
K_线性组合_数学,线性组合
K_线性结构_计算机科学技术,线性结构
K_线性表_计算机科学技术,线性表
K_线段_数学,线段
K_终身学习_计算机科学技术,终身学习
K_结点_数学,结点
K_绝对值_数学,绝对值
K_绝对地址_计算机科学技术,绝对地址
K_继承_计算机科学技术,继承
K_继承属性_计算机科学技术,继承属性
K_缓存_计算机科学技术,缓存
K_编址_计算机科学技术,编址
K_编码器_数学,编码器
K_网络体系结构_计算机科学技术,网络体系结构
K_网络图_数学,网络图
K_网络地址转换_计算机科学技术,网络地址转换
K_网络延时_计算机科学技术,网络延时
K_网络攻击_计算机科学技术,网络攻击
K_网络流_数学,网络流
K_网络规模_计算机科学技术,网络规模
K_翻译程序_计算机科学技术,翻译程序
K_联合分布_数学,联合分布
K_背包问题_计算机科学技术,背包问题
K_自动推理_计算机科学技术,自动推理
K_自动机_数学,自动机
K_自助法_数学,自助法
K_自变量_数学,自变量
K_自底向上_计算机科学技术,自底向上
K_自治系统_计算机科学技术,自治系统
K_自然对数_数学,自然对数
K_自组织神经网络_计算机科学技术,自组织神经网络
K_自适应学习_计算机科学技术,自适应学习
K_范数_数学,范数
K_蒙特卡洛方法_计算机科学技术,蒙特卡洛方法
K_虚拟化_计算机科学技术,虚拟化
K_虚拟地址_计算机科学技术,虚拟地址
K_虚拟地址空间_计算机科学技术,虚拟地址空间
K_虚拟存储管理_计算机科学技术,虚拟存储管理
K_行向量_数学,行向量
K_行地址_计算机科学技术,行地址
K_行松弛_数学,行松弛
K_行距离_数学,行距离
K_行迭代_数学,行迭代
K_补集_数学,补集
K_被加数_数学,被加数
K_覆盖网络_计算机科学技术,覆盖网络
K_解码器_计算机科学技术,解码器
K_解释程序_计算机科学技术,解释程序
K_计算复杂性_数学,计算复杂性
K_计算密集型任务_计算机科学技术,计算密集型任务
K_T_计算机应用_计算机科学技术,计算机应用
K_计算机技术_计算机科学技术,计算机技术
K_计算机系统_计算机科学技术,计算机系统
K_计算机视觉_计算机科学技术,计算机视觉
K_训练集_计算机科学技术,训练集
K_访问冲突_计算机科学技术,访问冲突
K_访问控制_计算机科学技术,访问控制
K_访问路径_计算机科学技术,访问路径
K_语义_数学,语义
K_语法错误_计算机科学技术,语法错误
K_语音识别_计算机科学技术,语音识别
K_误差逆传播算法_计算机科学技术,误差逆传播算法
K_调和级数_数学,调和级数
K_调度策略_计算机科学技术,调度策略
K_贝叶斯估计_数学,贝叶斯估计
K_贝叶斯公式_数学,贝叶斯公式
K_资源分配_计算机科学技术,资源分配
K_赋值_数学,赋值
K_赋值语句_计算机科学技术,赋值语句
K_超曲面_数学,超曲面
K_路由信息协议_计算机科学技术,路由信息协议
K_跳转指令_计算机科学技术,跳转指令
K_转义字符_计算机科学技术,转义字符
K_转移概率_数学,转移概率
K_软件中断_计算机科学技术,软件中断
K_输入规模_计算机科学技术,输入规模
K_输入输出_计算机科学技术,输入输出
K_输出_数学,输出
K_输出反馈_数学,输出反馈
K_边缘运算_数学,边缘运算
K_过拟合_计算机科学技术,过拟合
K_近似算法_计算机科学技术,近似算法
K_近似解_数学,近似解
K_进程间通信_计算机科学技术,进程间通信
K_远程访问_计算机科学技术,远程访问
K_连续时间系统_数学,连续时间系统
K_连通图_数学,连通图
K_连通域_计算机科学技术,连通域
K_迭代循环_数学,迭代循环
K_迭代计算_数学,迭代计算
K_选择排序_计算机科学技术,选择排序
K_递归函数_数学,递归函数
K_递归分析_数学,递归分析
K_递归性_数学,递归性
K_递归调用_计算机科学技术,递归调用
K_递推关系_计算机科学技术,递推关系
K_通解_数学,通解
K_逻辑单元_计算机科学技术,逻辑单元
K_逻辑地址_计算机科学技术,逻辑地址
K_逻辑地址空间_计算机科学技术,逻辑地址空间
K_逻辑斯谛分布_数学,逻辑斯谛分布
K_逻辑斯谛回归_数学,逻辑斯谛回归
K_逻辑结构_计算机科学技术,逻辑结构
K_逻辑表达式_数学,逻辑表达式
K_遍历过程_数学,遍历过程
K_遍历链_数学,遍历链
K_遗传算法_计算机科学技术,遗传算法
K_邻域_数学,邻域
K_邻接矩阵_数学,邻接矩阵
K_邻接表_计算机科学技术,邻接表
K_采样_计算机科学技术,采样
K_重放攻击_计算机科学技术,重放攻击
K_重构_数学,重构
K_鉴别数据_计算机科学技术,鉴别数据
K_链式法则_数学,链式法则
K_链表_计算机科学技术,链表
K_闭区间_数学,闭区间
K_队列长度_数学,队列长度
K_队头_计算机科学技术,队头
K_队尾_计算机科学技术,队尾
K_阶乘_数学,阶乘
K_除法_数学,除法
K_随机存取机_计算机科学技术,随机存取机
K_随机序列_数学,随机序列
K_随机搜索_数学,随机搜索
K_随机数_数学,随机数
K_随机梯度下降法_计算机科学技术,随机梯度下降法
K_随机游动_数学,随机游动
K_随机游走_计算机科学技术,随机游走
K_隶属关系_数学,隶属关系
K_集成测试_计算机科学技术,集成测试
K_静态IP地址_计算机科学技术,静态IP地址
K_静态语义_计算机科学技术,静态语义
K_非屏蔽双绞线_计算机科学技术,非屏蔽双绞线
K_非线性映射_数学,非线性映射
K_非线性模型_数学,非线性模型
K_非线性结构_计算机科学技术,非线性结构
K_面向对象方法_计算机科学技术,面向对象方法
K_面向对象程序设计_计算机科学技术,面向对象程序设计
K_顺序存储_计算机科学技术,顺序存储
K_顺序查找_计算机科学技术,顺序查找
K_预分配_计算机科学技术,预分配
K_频域_数学,频域
K_高级加密标准_计算机科学技术,高级加密标准
K_高级移动电话系统_计算机科学技术,高级移动电话系统
K_高频交易_计算机科学技术,高频交易
K_NP难问题_管理科学技术,NP难问题
K_p值_管理科学技术,p值
K_一致性_管理科学技术,一致性
K_价值函数_管理科学技术,价值函数
K_信息获取_管理科学技术,信息获取
K_元数据_管理科学技术,元数据
K_决策问题_管理科学技术,决策问题
K_分拆_管理科学技术,分拆
K_分类器_管理科学技术,分类器
K_创新流程_管理科学技术,创新流程
K_加密技术_管理科学技术,加密技术
K_单向传递_管理科学技术,单向传递
K_变更_管理科学技术,变更
K_可用性_管理科学技术,可用性
K_可视化分析_管理科学技术,可视化分析
K_可重用性_管理科学技术,可重用性
K_可预测性_管理科学技术,可预测性
K_启发式_管理科学技术,启发式
K_响应性_管理科学技术,响应性
K_响应时间_管理科学技术,响应时间
K_基向量_管理科学技术,基向量
K_复杂性_管理科学技术,复杂性
K_外部资源_管理科学技术,外部资源
K_学习曲线_管理科学技术,学习曲线
K_安全协议_管理科学技术,安全协议
K_容差_管理科学技术,容差
K_对偶问题_管理科学技术,对偶问题
K_开放性_管理科学技术,开放性
K_数据共享_管理科学技术,数据共享
K_数据库技术_管理科学技术,数据库技术
K_数据模型_管理科学技术,数据模型
K_最优方案_管理科学技术,最优方案
K_标准输出_管理科学技术,标准输出
K_概念图_管理科学技术,概念图
K_模块_管理科学技术,模块
K_模型方法_管理科学技术,模型方法
K_流程控制_管理科学技术,流程控制
K_深度_管理科学技术,深度
K_牛顿法_管理科学技术,牛顿法
K_状态图_管理科学技术,状态图
K_状态转移概率_管理科学技术,状态转移概率
K_环境影响_管理科学技术,环境影响
K_白盒测试_管理科学技术,白盒测试
K_相关性原理_管理科学技术,相关性原理
K_知识_管理科学技术,知识
K_知识共享_管理科学技术,知识共享
K_知识表示_管理科学技术,知识表示
K_矩估计_管理科学技术,矩估计
K_神经网络模型_管理科学技术,神经网络模型
K_神经网络算法_管理科学技术,神经网络算法
K_空间管理_管理科学技术,空间管理
K_管理子系统_管理科学技术,管理子系统
K_系统结构_管理科学技术,系统结构
K_系统能力_管理科学技术,系统能力
K_索引_管理科学技术,索引
K_线性回归模型_管理科学技术,线性回归模型
K_网络安全_管理科学技术,网络安全
K_计数值_管理科学技术,计数值
K_贝叶斯决策_管理科学技术,贝叶斯决策
K_贝叶斯方法_管理科学技术,贝叶斯方法
K_转移概率矩阵_管理科学技术,转移概率矩阵
K_运行时间_管理科学技术,运行时间
K_迭代算法_管理科学技术,迭代算法
K_逻辑关系_管理科学技术,逻辑关系
K_重复实验_管理科学技术,重复实验
K_重组_管理科学技术,重组
K_预测误差_管理科学技术,预测误差
K_马尔可夫链_管理科学技术,马尔可夫链
K_黑箱_管理科学技术,黑箱
K_随机性_自然辩证法,随机性
