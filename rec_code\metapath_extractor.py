import json
import numpy as np
from collections import defaultdict
import torch
import pickle
import os
from typing import Dict, List, Tuple

class MetaPathSubgraphExtractor:
    """
    元路径子图提取器，用于提取视频和用户的多重意图子图
    """
    
    def __init__(self, dataset, cache_dir="cache"):
        self.dataset = dataset
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
        # 元路径定义
        self.meta_paths = {
            'V-C': {'weight': 0.25, 'description': '视频-概念直接关联'},
            'V-Course': {'weight': 0.20, 'description': '视频-课程归属'},
            'V-C-Pre-C': {'weight': 0.20, 'description': '视频-概念-先修概念'},
            'V-Course-Teacher': {'weight': 0.15, 'description': '视频-课程-教师'},
            'V-C-Concept': {'weight': 0.10, 'description': '视频-概念-相关概念'},
            'V-Course-Course': {'weight': 0.10, 'description': '视频-课程-相关课程'}
        }
        
        print(f"Initialized MetaPath extractor with {len(self.meta_paths)} meta-paths")
    
    def extract_video_metapath_subgraph(self, video_id: str, max_neighbors: int = 30) -> Dict:
        """
        基于元路径提取视频中心子图
        
        Args:
            video_id: 视频ID（原始ID）
            max_neighbors: 每个元路径的最大邻居数
            
        Returns:
            包含多个元路径邻居的字典
        """
        if video_id not in self.dataset.video_id_map:
            return {}
        
        subgraph_info = {
            'video_id': video_id,
            'video_name': self.dataset.video_info.get(video_id, {}).get('name', ''),
            'metapath_neighbors': {}
        }
        
        # 元路径1: V-C (视频-概念)
        concepts = self.dataset.kg_relations['video_concept'].get(video_id, [])
        concept_info = []
        for concept_id in concepts[:max_neighbors]:
            if concept_id in self.dataset.concept_info:
                concept_info.append({
                    'id': concept_id,
                    'name': self.dataset.concept_info[concept_id].get('name', ''),
                    'description': self.dataset.concept_info[concept_id].get('description', '')
                })
        subgraph_info['metapath_neighbors']['V-C'] = concept_info
        
        # 元路径2: V-Course (视频-课程)
        courses = []
        for course_id, video_list in self.dataset.kg_relations['course_video'].items():
            if video_id in video_list:
                if course_id in self.dataset.course_info:
                    courses.append({
                        'id': course_id,
                        'name': self.dataset.course_info[course_id].get('name', ''),
                        'description': self.dataset.course_info[course_id].get('description', '')
                    })
        subgraph_info['metapath_neighbors']['V-Course'] = courses[:max_neighbors]
        
        # 元路径3: V-C-Pre-C (视频-概念-先修概念)
        prerequisite_concepts = []
        for concept_id in concepts:
            # 找到这个概念的先修概念
            prereqs = self.dataset.kg_relations['prerequisite'].get(concept_id, [])
            for prereq_id in prereqs:
                if prereq_id in self.dataset.concept_info:
                    prerequisite_concepts.append({
                        'id': prereq_id,
                        'name': self.dataset.concept_info[prereq_id].get('name', ''),
                        'relation': f'先修于{self.dataset.concept_info[concept_id].get("name", "")}'
                    })
        subgraph_info['metapath_neighbors']['V-C-Pre-C'] = prerequisite_concepts[:max_neighbors]
        
        # 元路径4: V-Course-Teacher (视频-课程-教师)
        teachers = []
        for course_info in subgraph_info['metapath_neighbors']['V-Course']:
            course_id = course_info['id']
            for teacher_id, course_list in self.dataset.kg_relations['teacher_course'].items():
                if course_id in course_list and teacher_id in self.dataset.teacher_info:
                    teachers.append({
                        'id': teacher_id,
                        'name': self.dataset.teacher_info[teacher_id].get('name', ''),
                        'course': course_info['name']
                    })
        subgraph_info['metapath_neighbors']['V-Course-Teacher'] = teachers[:max_neighbors]
        
        return subgraph_info
    
    def extract_user_neighbor_subgraph(self, user_idx: int, max_videos: int = 50) -> Dict:
        """
        提取用户在二分图上的一阶邻居子图
        
        Args:
            user_idx: 用户索引
            max_videos: 最大视频数量
            
        Returns:
            用户邻居子图信息
        """
        if user_idx not in self.dataset.allPos:
            return {}
        
        # 获取用户观看的视频
        watched_video_indices = self.dataset.allPos[user_idx]
        
        user_subgraph = {
            'user_idx': user_idx,
            'user_id': self.dataset.idx_to_user.get(user_idx, ''),
            'watched_videos': [],
            'learning_sequence': [],
            'concept_distribution': defaultdict(int),
            'course_distribution': defaultdict(int)
        }
        
        # 收集每个视频的详细信息
        for video_idx in watched_video_indices[:max_videos]:
            video_id = self.dataset.idx_to_video.get(video_idx, '')
            if not video_id:
                continue
                
            video_info = {
                'video_idx': video_idx,
                'video_id': video_id,
                'video_name': self.dataset.video_info.get(video_id, {}).get('name', ''),
                'concepts': [],
                'courses': []
            }
            
            # 获取视频的概念
            concepts = self.dataset.kg_relations['video_concept'].get(video_id, [])
            for concept_id in concepts:
                if concept_id in self.dataset.concept_info:
                    concept_name = self.dataset.concept_info[concept_id].get('name', '')
                    video_info['concepts'].append({
                        'id': concept_id,
                        'name': concept_name
                    })
                    user_subgraph['concept_distribution'][concept_name] += 1
            
            # 获取视频的课程
            for course_id, video_list in self.dataset.kg_relations['course_video'].items():
                if video_id in video_list and course_id in self.dataset.course_info:
                    course_name = self.dataset.course_info[course_id].get('name', '')
                    video_info['courses'].append({
                        'id': course_id,
                        'name': course_name
                    })
                    user_subgraph['course_distribution'][course_name] += 1
            
            user_subgraph['watched_videos'].append(video_info)
        
        # 构建学习序列（按观看顺序）
        # 这里简化处理，实际可以根据timestamp排序
        user_subgraph['learning_sequence'] = [
            {
                'video_name': video['video_name'],
                'concepts': [c['name'] for c in video['concepts']],
                'courses': [c['name'] for c in video['courses']]
            }
            for video in user_subgraph['watched_videos']
        ]
        
        return user_subgraph
    
    def convert_subgraph_to_text(self, subgraph_info: Dict, subgraph_type: str = 'video') -> str:
        """
        将子图信息转换为文本描述
        
        Args:
            subgraph_info: 子图信息字典
            subgraph_type: 'video' 或 'user'
            
        Returns:
            文本描述
        """
        if subgraph_type == 'video':
            return self._video_subgraph_to_text(subgraph_info)
        elif subgraph_type == 'user':
            return self._user_subgraph_to_text(subgraph_info)
        else:
            raise ValueError("subgraph_type must be 'video' or 'user'")
    
    def _video_subgraph_to_text(self, subgraph_info: Dict) -> str:
        """将视频子图转换为文本"""
        if not subgraph_info:
            return ""
        
        text_parts = []
        video_name = subgraph_info.get('video_name', '未知视频')
        text_parts.append(f"视频名称：{video_name}")
        
        # 相关概念
        concepts = subgraph_info.get('metapath_neighbors', {}).get('V-C', [])
        if concepts:
            concept_names = [c.get('name', '') for c in concepts if c.get('name')]
            text_parts.append(f"相关概念：{', '.join(concept_names[:10])}")
        
        # 所属课程
        courses = subgraph_info.get('metapath_neighbors', {}).get('V-Course', [])
        if courses:
            course_names = [c.get('name', '') for c in courses if c.get('name')]
            text_parts.append(f"所属课程：{', '.join(course_names[:5])}")
        
        # 授课教师
        teachers = subgraph_info.get('metapath_neighbors', {}).get('V-Course-Teacher', [])
        if teachers:
            teacher_names = [t.get('name', '') for t in teachers if t.get('name')]
            text_parts.append(f"授课教师：{', '.join(teacher_names[:5])}")
        
        # 先修概念
        prereqs = subgraph_info.get('metapath_neighbors', {}).get('V-C-Pre-C', [])
        if prereqs:
            prereq_names = [p.get('name', '') for p in prereqs if p.get('name')]
            text_parts.append(f"先修概念：{', '.join(prereq_names[:5])}")
        
        return "；".join(text_parts)
    
    def _user_subgraph_to_text(self, subgraph_info: Dict) -> str:
        """将用户子图转换为文本"""
        if not subgraph_info:
            return ""
        
        text_parts = []
        
        # 观看视频数量
        video_count = len(subgraph_info.get('watched_videos', []))
        text_parts.append(f"观看视频数量：{video_count}")
        
        # 学习的主要概念
        concept_dist = subgraph_info.get('concept_distribution', {})
        if concept_dist:
            top_concepts = sorted(concept_dist.items(), key=lambda x: x[1], reverse=True)[:10]
            concept_text = ', '.join([f"{name}({count}次)" for name, count in top_concepts])
            text_parts.append(f"主要学习概念：{concept_text}")
        
        # 学习的主要课程
        course_dist = subgraph_info.get('course_distribution', {})
        if course_dist:
            top_courses = sorted(course_dist.items(), key=lambda x: x[1], reverse=True)[:5]
            course_text = ', '.join([f"{name}({count}个视频)" for name, count in top_courses])
            text_parts.append(f"主要学习课程：{course_text}")
        
        # 最近观看的视频
        recent_videos = subgraph_info.get('watched_videos', [])[-5:]
        if recent_videos:
            recent_names = [v.get('video_name', '') for v in recent_videos if v.get('video_name')]
            text_parts.append(f"最近观看：{', '.join(recent_names)}")
        
        return "；".join(text_parts)
    
    def batch_extract_video_subgraphs(self, video_ids: List[str], max_neighbors: int = 30) -> Dict[str, Dict]:
        """批量提取视频子图"""
        cache_file = os.path.join(self.cache_dir, f"video_subgraphs_{max_neighbors}.pkl")
        
        if os.path.exists(cache_file):
            print("Loading video subgraphs from cache...")
            with open(cache_file, 'rb') as f:
                cached_subgraphs = pickle.load(f)
            
            # 检查是否需要提取新的视频
            missing_videos = [vid for vid in video_ids if vid not in cached_subgraphs]
            if not missing_videos:
                return {vid: cached_subgraphs[vid] for vid in video_ids if vid in cached_subgraphs}
        else:
            cached_subgraphs = {}
            missing_videos = video_ids
        
        print(f"Extracting subgraphs for {len(missing_videos)} videos...")
        
        # 提取缺失的视频子图
        for i, video_id in enumerate(missing_videos):
            if i % 1000 == 0:
                print(f"Processed {i}/{len(missing_videos)} videos")
            
            subgraph = self.extract_video_metapath_subgraph(video_id, max_neighbors)
            if subgraph:
                cached_subgraphs[video_id] = subgraph
        
        # 保存到缓存
        with open(cache_file, 'wb') as f:
            pickle.dump(cached_subgraphs, f)
        print(f"Video subgraphs cached to {cache_file}")
        
        return {vid: cached_subgraphs[vid] for vid in video_ids if vid in cached_subgraphs}
    
    def batch_extract_user_subgraphs(self, user_indices: List[int], max_videos: int = 50) -> Dict[int, Dict]:
        """批量提取用户子图"""
        cache_file = os.path.join(self.cache_dir, f"user_subgraphs_{max_videos}.pkl")
        
        if os.path.exists(cache_file):
            print("Loading user subgraphs from cache...")
            with open(cache_file, 'rb') as f:
                cached_subgraphs = pickle.load(f)
            
            missing_users = [uid for uid in user_indices if uid not in cached_subgraphs]
            if not missing_users:
                return {uid: cached_subgraphs[uid] for uid in user_indices if uid in cached_subgraphs}
        else:
            cached_subgraphs = {}
            missing_users = user_indices
        
        print(f"Extracting subgraphs for {len(missing_users)} users...")
        
        # 提取缺失的用户子图
        for i, user_idx in enumerate(missing_users):
            if i % 1000 == 0:
                print(f"Processed {i}/{len(missing_users)} users")
            
            subgraph = self.extract_user_neighbor_subgraph(user_idx, max_videos)
            if subgraph:
                cached_subgraphs[user_idx] = subgraph
        
        # 保存到缓存
        with open(cache_file, 'wb') as f:
            pickle.dump(cached_subgraphs, f)
        print(f"User subgraphs cached to {cache_file}")
        
        return {uid: cached_subgraphs[uid] for uid in user_indices if uid in cached_subgraphs}
