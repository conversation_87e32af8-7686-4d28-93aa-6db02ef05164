#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本，验证多重意图视频推荐系统的基本功能
"""

import os
import sys
import torch
import numpy as np

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_loading():
    """测试数据加载"""
    print("=== 测试MOOCCube数据加载 ===")
    
    try:
        from mooccube_dataloader import MOOCCubeDataset
        
        # 检查数据目录
        data_path = "../data/MOOCCube"
        if not os.path.exists(data_path):
            print(f"错误: 数据目录 {data_path} 不存在")
            print("请确保MOOCCube数据集已正确放置")
            return False
        
        # 检查关键文件
        required_files = [
            "entities/user.json",
            "entities/video.json", 
            "entities/concept.json",
            "entities/course.json",
            "entities/teacher.json",
            "relations/user-video.json",
            "relations/video-concept.json",
            "relations/course-video.json"
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = os.path.join(data_path, file_path)
            if not os.path.exists(full_path):
                # 尝试alternative路径
                alt_path = os.path.join(data_path, "additional_information", "user_video_act.json")
                if file_path == "relations/user-video.json" and os.path.exists(alt_path):
                    continue
                missing_files.append(file_path)
        
        if missing_files:
            print(f"警告: 缺少以下文件: {missing_files}")
        
        print("正在加载数据集...")
        dataset = MOOCCubeDataset(
            path=data_path,
            min_interactions=5,  # 降低阈值便于测试
            cache_dir="cache"
        )
        
        print(f"✓ 数据集加载成功!")
        print(f"  用户数: {dataset.n_users}")
        print(f"  视频数: {dataset.m_items}")
        print(f"  训练交互数: {dataset.trainDataSize}")
        print(f"  测试用户数: {len(dataset.testDict)}")
        
        # 测试数据访问
        if dataset.n_users > 0 and dataset.m_items > 0:
            sample_user = 0
            sample_item = 0
            print(f"  样本用户 {sample_user} 的正向交互数: {len(dataset.allPos.get(sample_user, []))}")
            print(f"  样本用户 {sample_user} 的测试项目数: {len(dataset.testDict.get(sample_user, []))}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_metapath_extraction():
    """测试元路径提取"""
    print("\n=== 测试元路径子图提取 ===")
    
    try:
        from mooccube_dataloader import MOOCCubeDataset
        from metapath_extractor import MetaPathSubgraphExtractor
        
        dataset = MOOCCubeDataset(
            path="../data/MOOCCube",
            min_interactions=5,
            cache_dir="cache"
        )
        
        extractor = MetaPathSubgraphExtractor(dataset, cache_dir="cache")
        
        # 测试视频子图提取
        video_ids = list(dataset.video_id_map.keys())[:3]  # 只测试3个视频
        print(f"测试 {len(video_ids)} 个视频的子图提取...")
        
        for video_id in video_ids:
            subgraph = extractor.extract_video_metapath_subgraph(video_id, max_neighbors=10)
            if subgraph:
                text = extractor.convert_subgraph_to_text(subgraph, 'video')
                print(f"  视频 {video_id}: {text[:80]}...")
            else:
                print(f"  视频 {video_id}: 无子图数据")
        
        # 测试用户子图提取
        user_indices = list(range(min(3, dataset.n_users)))
        print(f"测试 {len(user_indices)} 个用户的子图提取...")
        
        for user_idx in user_indices:
            subgraph = extractor.extract_user_neighbor_subgraph(user_idx, max_videos=20)
            if subgraph:
                text = extractor.convert_subgraph_to_text(subgraph, 'user')
                print(f"  用户 {user_idx}: {text[:80]}...")
            else:
                print(f"  用户 {user_idx}: 无子图数据")
        
        print("✓ 元路径提取测试成功!")
        return True
        
    except Exception as e:
        print(f"✗ 元路径提取失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n=== 测试模型创建 ===")
    
    try:
        from mooccube_dataloader import MOOCCubeDataset
        from multi_intent_model import MultiIntentVideoRecommender
        
        dataset = MOOCCubeDataset(
            path="../data/MOOCCube",
            min_interactions=5,
            cache_dir="cache"
        )
        
        # 创建模拟嵌入
        video_intent_embeddings = {}
        user_intent_embeddings = {}
        
        # 为所有视频创建随机嵌入
        for video_idx in range(dataset.m_items):
            video_id = dataset.idx_to_video.get(video_idx, f"video_{video_idx}")
            video_intent_embeddings[video_id] = np.random.randn(384).tolist()
        
        # 为所有用户创建随机嵌入
        for user_idx in range(dataset.n_users):
            user_intent_embeddings[user_idx] = np.random.randn(384).tolist()
        
        # 配置
        config = {
            'latent_dim_rec': 64,
            'intent_dim': 384,
            'hidden_dim': 512,
            'final_dim': 64,
            'lightGCN_n_layers': 3,
            'dropout': 0,
            'A_split': False,
            'A_n_fold': 100,
            'temperature': 0.1
        }
        
        # 设置设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        # 创建模型
        model = MultiIntentVideoRecommender(
            config=config,
            dataset=dataset,
            video_intent_embeddings=video_intent_embeddings,
            user_intent_embeddings=user_intent_embeddings
        ).to(device)
        
        print(f"✓ 模型创建成功!")
        print(f"  参数数量: {sum(p.numel() for p in model.parameters()):,}")
        print(f"  可训练参数: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
        
        # 测试前向传播
        batch_size = 4
        users = torch.randint(0, dataset.n_users, (batch_size,)).to(device)
        pos_items = torch.randint(0, dataset.m_items, (batch_size,)).to(device)
        neg_items = torch.randint(0, dataset.m_items, (batch_size,)).to(device)
        
        print("测试前向传播...")
        with torch.no_grad():
            users_emb, pos_emb, neg_emb, contrastive_loss = model(users, pos_items, neg_items)
            
            print(f"  用户嵌入形状: {users_emb.shape}")
            print(f"  正样本嵌入形状: {pos_emb.shape}")
            print(f"  负样本嵌入形状: {neg_emb.shape}")
            print(f"  对比损失: {contrastive_loss.item():.4f}")
        
        # 测试预测
        print("测试预测...")
        with torch.no_grad():
            scores = model.predict(users, pos_items)
            print(f"  预测评分形状: {scores.shape}")
            print(f"  预测评分范围: [{scores.min().item():.4f}, {scores.max().item():.4f}]")
        
        print("✓ 模型测试成功!")
        return True
        
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始快速测试多重意图视频推荐系统")
    print("=" * 60)
    
    # 创建缓存目录
    os.makedirs("cache", exist_ok=True)
    
    tests = [
        ("数据加载", test_data_loading),
        ("元路径提取", test_metapath_extraction),
        ("模型创建", test_model_creation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"{test_name} 测试出现异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结:")
    for test_name, success in results.items():
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    passed = sum(results.values())
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 系统基本功能正常。")
        print("可以开始完整训练: bash train_mooccube.sh")
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
