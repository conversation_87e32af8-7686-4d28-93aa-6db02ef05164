# CoLaKG: 基于大语言模型的知识图谱理解推荐系统

## 1. 项目概述

CoLaKG（Comprehending Knowledge Graphs with Large Language Models for Recommender Systems）是一个利用大型语言模型理解知识图谱来增强推荐系统的框架。该项目将知识图谱的结构化信息转换为自然语言描述，通过LLM生成语义理解，再将其转化为嵌入向量用于推荐模型训练。

## 2. 整体架构流程

### 2.1 数据预处理阶段
```
原始数据 → 知识图谱构建 → 子图提取 → 文本化 → LLM理解 → 语义嵌入
```

### 2.2 模型训练阶段
```
语义嵌入 → 相似度计算 → 邻居图构建 → 注意力聚合 → LightGCN → 推荐预测
```

## 3. 核心模块详细分析

### 3.1 LLM语义理解模块 (`llm_code/`)

#### 3.1.1 LLM请求模块 (`llm_request_api.py`)
**功能**: 将知识图谱子图转换为自然语言提示，调用LLM API获取语义理解

**输入**: 
- `llm_input_item.json`: 包含项目的KG子图三元组文本
- `llm_input_user.json`: 包含用户历史交互的文本描述

**处理流程**:
1. 根据数据集类型设置专门的系统提示词
2. 异步批量调用DeepSeek API (并发限制100)
3. 使用temperature=0.0确保输出确定性

**输出**: 
- `llm_response_item.json`: LLM对项目的语义理解文本
- `llm_response_user.json`: LLM对用户偏好的分析文本

**关键代码逻辑**:
```python
# 项目语义理解提示模板（以MovieLens为例）
system_input = "Assume you are an expert in movie recommendation. 
You will be given a certain movie with its first-order information 
(in the form of triples) and some second-order relationships..."
```

#### 3.1.2 文本嵌入模块 (`get_text_embedding.py`)
**功能**: 将LLM生成的文本转换为高维语义嵌入向量

**输入**: LLM响应的JSON文件
**模型**: princeton-nlp/sup-simcse-roberta-large (1024维)
**输出**: `.pt`格式的嵌入张量文件

**处理流程**:
1. 批量加载文本 (batch_size=64)
2. 使用SimCSE模型编码为1024维向量
3. 保存为PyTorch张量格式

### 3.2 推荐模型模块 (`rec_code/`)

#### 3.2.1 数据加载器 (`dataloader.py`)
**核心类**: `Loader` (继承自`BasicDataset`)

**功能**: 
- 加载用户-项目交互数据
- 构建稀疏图结构用于GCN传播
- 提供训练/测试数据接口

**数据格式**:
- `train.txt`: 每行格式为 `user_id item1 item2 item3 ...`
- `test.txt`: 同上格式，用于测试

**关键方法**:
- `getSparseGraph()`: 构建NGCF形式的归一化邻接矩阵
- `getUserPosItems()`: 获取用户正向交互项目
- `__build_test()`: 构建测试数据字典

#### 3.2.2 CoLaKG模型 (`model.py`)
**核心类**: `CoLaKG` (继承自`BasicModel`)

**初始化参数**:
- `adj_matrix`: 项目间语义相似度邻接矩阵 (Top-k邻居)
- `semantic_emb`: 项目语义嵌入 (1024维)
- `user_semantic_emb`: 用户语义嵌入 (1024维)

**关键组件**:

1. **语义映射层**:
```python
self.semantic_map = nn.Linear(1024, latent_dim)      # 项目语义映射
self.user_semantic_map = nn.Linear(1024, latent_dim) # 用户语义映射
```

2. **注意力机制**:
```python
self.W = nn.Parameter(torch.empty(size=(1024, 32)))  # 注意力权重
self.a = nn.Parameter(torch.empty(size=(2*32, 1)))   # 注意力向量
```

3. **嵌入融合**:
```python
# ID嵌入与语义嵌入平均融合
items_emb_merged = (items_emb + items_semantic_emb) / 2
users_emb_merged = (users_emb + user_semantic_emb) / 2
```

4. **邻居聚合**:
```python
# 基于语义相似度的注意力聚合
attention = F.softmax(torch.matmul(W_concat, self.a).squeeze(-1), dim=1)
h_prime = torch.sum(attention.unsqueeze(-1) * value_emb, dim=1)
```

#### 3.2.3 训练流程 (`main.py`)

**主要步骤**:

1. **语义邻接矩阵构建**:
```python
# 计算项目语义嵌入间余弦相似度
cosine_sim_matrix = cosine_similarity(item_semantic_emb.numpy())
# 选择Top-k相似邻居
sorted_indices = np.argsort(-cosine_sim_matrix, axis=1)[:, 1:k+1]
```

2. **模型初始化**:
```python
Recmodel = register.MODELS[world.model_name](
    world.config, dataset, sorted_indices, 
    item_semantic_emb, user_semantic_emb
)
```

3. **训练循环**:
```python
for epoch in range(world.TRAIN_epochs):
    if epoch % 5 == 0:
        test_results = Procedure.Test(dataset, Recmodel, epoch, w, world.config['multicore'])
    output_information = Procedure.BPR_train_original(dataset, Recmodel, bpr, epoch, neg_k=Neg_k, w=w)
```

#### 3.2.4 BPR训练 (`Procedure.py`)

**训练流程**:
1. 均匀采样用户-正向项-负向项三元组
2. 分批计算BPR损失
3. 反向传播更新参数

**损失函数**:
```python
loss = torch.mean(torch.nn.functional.softplus(neg_scores - pos_scores))
reg_loss = (1/2) * (用户嵌入正则 + 项目嵌入正则 + 语义嵌入正则) / batch_size
```

## 4. 数据流向图

```mermaid
graph TD
    A[原始KG数据] --> B[子图提取]
    B --> C[文本化三元组]
    C --> D[LLM API调用]
    D --> E[语义理解文本]
    E --> F[SimCSE编码]
    F --> G[语义嵌入向量]
    
    H[用户-项目交互] --> I[数据加载器]
    G --> J[余弦相似度计算]
    J --> K[Top-k邻居图]
    
    I --> L[CoLaKG模型]
    G --> L
    K --> L
    
    L --> M[注意力聚合]
    M --> N[LightGCN传播]
    N --> O[BPR损失训练]
    O --> P[推荐预测]
```

## 5. 模块间输入输出关系

### 5.1 LLM模块
- **输入**: KG子图三元组文本
- **输出**: 语义理解文本 → 1024维嵌入向量

### 5.2 相似度计算模块
- **输入**: 项目语义嵌入矩阵 (N×1024)
- **输出**: Top-k邻接矩阵 (N×k)

### 5.3 CoLaKG模型
- **输入**: 
  - 用户-项目交互图
  - 项目语义嵌入 (N×1024)
  - 用户语义嵌入 (M×1024)  
  - 语义邻接矩阵 (N×k)
- **输出**: 用户-项目评分预测

### 5.4 训练模块
- **输入**: 训练三元组 (user, pos_item, neg_item)
- **输出**: 更新后的模型参数

## 6. 关键超参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `latent_dim_rec` | 64 | ID嵌入维度 |
| `neighbor_k` | 30 | 语义邻居数量 |
| `dropout_i/u/n` | 0.6 | 各类嵌入dropout率 |
| `lightGCN_n_layers` | 3 | GCN层数 |
| `lr` | 0.001 | 学习率 |
| `bpr_batch_size` | 4096 | BPR批大小 |

## 7. MOOCCube数据集可行性分析

### 7.1 数据集结构分析

MOOCCube数据集包含以下实体和关系：

**实体类型**:
- `user.json`: 用户信息
- `course.json`: 课程信息  
- `video.json`: 视频信息
- `concept.json`: 概念信息
- `teacher.json`: 教师信息
- `school.json`: 学校信息
- `paper.json`: 论文信息

**关系类型**:
- `user-course.json`: 用户-课程交互
- `user-video.json`: 用户-视频交互
- `course-concept.json`: 课程-概念关系
- `course-video.json`: 课程-视频关系
- `video-concept.json`: 视频-概念关系
- `teacher-course.json`: 教师-课程关系
- `school-course.json`: 学校-课程关系
- `concept-paper.json`: 概念-论文关系
- `prerequisite-dependency.json`: 先修依赖关系

### 7.2 适配CoLaKG的可行性

**✅ 优势**:
1. **丰富的知识图谱结构**: MOOCCube提供了完整的教育领域知识图谱，包含课程、概念、教师等多种实体及其关系
2. **用户交互数据**: 包含用户-课程和用户-视频的交互记录，可构建推荐任务
3. **层次化概念体系**: 概念间的先修依赖关系为教育推荐提供了重要的序列化信息
4. **多模态信息**: 结合了课程内容、教师背景、学校信息等多维度特征

**⚠️ 挑战**:
1. **缺少KG构建代码**: 当前代码库没有从原始JSON文件构建知识图谱的预处理脚本
2. **数据格式转换**: 需要将MOOCCube的JSON格式转换为CoLaKG所需的训练数据格式
3. **提示词设计**: 需要为教育领域设计专门的LLM提示模板
4. **评估指标**: 教育推荐可能需要考虑学习路径、难度递进等特殊指标

### 7.3 实现建议

**必需的预处理步骤**:

1. **构建用户-项目交互矩阵**:
   - 从`user-course.json`提取用户-课程交互
   - 或从`user-video.json`提取用户-视频交互
   - 转换为`train.txt`和`test.txt`格式

2. **构建项目知识子图**:
   - 对每个课程/视频，提取其一阶邻居（相关概念、教师、学校等）
   - 提取二阶邻居（相关课程、先修课程等）
   - 转换为三元组文本格式

3. **设计教育领域提示词**:
```python
system_input = "假设你是在线教育推荐专家。给定一门课程及其相关信息（概念、教师、先修关系等），
请分析该课程的特点、适合的学习者类型，以及学习该课程需要的前置知识。回答应该是连贯的段落，不超过200字。"
```

4. **构建用户学习历史**:
   - 整合用户的课程学习记录
   - 包含课程信息、概念标签、学习时间等
   - 用于生成用户偏好的语义理解

**预期效果**:
MOOCCube数据集非常适合个性化学习资源推荐任务，其丰富的知识图谱结构和教育领域特性使得CoLaKG方法具有很好的应用前景。通过适当的数据预处理和提示词设计，可以实现基于概念理解的智能课程推荐系统。

## 8. 技术实现细节

### 8.1 CoLaKG模型核心算法

#### 8.1.1 语义嵌入融合机制
```python
def computer(self):
    # 1. 获取原始ID嵌入
    users_emb = self.embedding_user.weight  # (M, d)
    items_emb = self.embedding_item.weight  # (N, d)

    # 2. 语义嵌入映射和融合
    items_semantic_emb = F.dropout(self.semantic_emb, self.dropout_i, training=self.training)
    items_semantic_emb = self.semantic_map(items_semantic_emb)  # (N, d)
    items_semantic_emb = F.elu(items_semantic_emb)
    items_emb_merged = (items_emb + items_semantic_emb) / 2  # 平均融合

    # 3. 用户语义嵌入处理（同理）
    user_semantic_emb = self.user_semantic_map(self.user_semantic_emb)
    users_emb_merged = (users_emb + user_semantic_emb) / 2
```

#### 8.1.2 语义邻居注意力聚合
```python
# 1. 获取语义邻居嵌入
neighbor_emb = items_emb_merged[self.adj_matrix]  # (N, k, d)
neighbor_semantic_emb = self.semantic_emb[self.adj_matrix]  # (N, k, 1024)

# 2. 注意力权重计算
Wh = torch.matmul(neighbor_semantic_emb, self.W)  # (N, k, 32)
h0 = items_semantic_emb0.unsqueeze(1).repeat(1, k, 1)  # (N, k, 1024)
Wh0 = torch.matmul(h0, self.W)  # (N, k, 32)
W_concat = torch.cat((Wh, Wh0), dim=-1)  # (N, k, 64)

# 3. 注意力分数和聚合
attention = F.softmax(torch.matmul(W_concat, self.a).squeeze(-1), dim=1)  # (N, k)
h_prime = torch.sum(attention.unsqueeze(-1) * neighbor_emb, dim=1)  # (N, d)

# 4. 最终项目表示
items_emb_final = (items_emb_merged + h_prime) / 2
```

#### 8.1.3 LightGCN图传播
```python
# 1. 构建用户-项目二部图
all_emb = torch.cat([users_emb_merged, items_emb_final])  # (M+N, d)
embs = [all_emb]

# 2. 多层图卷积传播
for layer in range(self.n_layers):
    all_emb = torch.sparse.mm(self.Graph, all_emb)  # 图卷积
    embs.append(all_emb)

# 3. 层级聚合
light_out = torch.mean(torch.stack(embs, dim=1), dim=1)  # 平均聚合
users_final, items_final = torch.split(light_out, [self.n_users, self.n_items])
```

### 8.2 训练优化策略

#### 8.2.1 BPR损失函数
```python
def bpr_loss(self, users, pos, neg):
    # 1. 获取嵌入表示
    users_emb, pos_emb, neg_emb, userEmb0, posEmb0, negEmb0, pos_emb_ego0, neg_emb_ego0, users_emb_ego0 = self.getEmbedding(users, pos, neg)

    # 2. 计算偏好分数
    pos_scores = torch.sum(users_emb * pos_emb, dim=1)
    neg_scores = torch.sum(users_emb * neg_emb, dim=1)

    # 3. BPR损失
    bpr_loss = torch.mean(F.softplus(neg_scores - pos_scores))

    # 4. L2正则化（包含语义嵌入）
    reg_loss = (1/2) * (userEmb0.norm(2).pow(2) + posEmb0.norm(2).pow(2) + negEmb0.norm(2).pow(2) +
                       pos_emb_ego0.norm(2).pow(2) + neg_emb_ego0.norm(2).pow(2) + users_emb_ego0.norm(2).pow(2)) / len(users)

    return bpr_loss, reg_loss
```

#### 8.2.2 负采样策略
```python
def UniformSample_original(dataset):
    """均匀负采样策略"""
    dataset: BasicDataset
    allPos = dataset.allPos  # 所有用户的正向交互
    start = time()
    S = []

    for user in range(dataset.n_users):
        posForUser = allPos[user]  # 用户的正向项目
        if len(posForUser) == 0: continue

        for pos_item in posForUser:
            while True:
                neg_item = np.random.randint(0, dataset.m_items)
                if neg_item not in posForUser:
                    break
            S.append([user, pos_item, neg_item])

    return np.array(S)
```

### 8.3 评估指标

#### 8.3.1 Top-K推荐指标
```python
def Test(dataset, Recmodel, epoch, w=None, multicore=0):
    """测试函数，计算Recall@K, Precision@K, NDCG@K"""
    u_batch_size = world.config['test_u_batch_size']
    dataset: utils.BasicDataset
    testDict: dict = dataset.testDict  # 测试数据

    # 批量预测
    max_K = max(world.topks)
    results = {'precision': np.zeros(len(world.topks)),
               'recall': np.zeros(len(world.topks)),
               'ndcg': np.zeros(len(world.topks))}

    with torch.no_grad():
        users = list(testDict.keys())
        users_list = []
        rating_list = []
        groundTrue_list = []

        for batch_users in utils.minibatch(users, batch_size=u_batch_size):
            allPos = dataset.getUserPosItems(batch_users)
            groundTrue = [testDict[u] for u in batch_users]

            batch_users_gpu = torch.Tensor(batch_users).long()
            batch_users_gpu = batch_users_gpu.to(world.device)

            rating = Recmodel.getUsersRating(batch_users_gpu)  # 获取评分
            exclude_index = []
            exclude_items = []

            # 排除训练集中的正向项目
            for range_i, items in enumerate(allPos):
                exclude_index.extend([range_i] * len(items))
                exclude_items.extend(items)

            rating[exclude_index, exclude_items] = -(1<<10)  # 设置为极小值
            _, rating_K = torch.topk(rating, k=max_K)  # Top-K推荐
            rating = rating.cpu().numpy()

            del rating_K
            users_list.append(batch_users)
            rating_list.append(rating)
            groundTrue_list.append(groundTrue)

    # 计算指标
    X = zip(rating_list, groundTrue_list)
    pre_results = []
    for x in X:
        pre_results.append(test_one_batch(x))

    # 聚合结果
    for result in pre_results:
        results['recall'] += result['recall']
        results['precision'] += result['precision']
        results['ndcg'] += result['ndcg']

    results['recall'] /= float(len(users))
    results['precision'] /= float(len(users))
    results['ndcg'] /= float(len(users))

    return results
```

## 9. MOOCCube数据集实现指南

### 9.1 数据预处理脚本框架

```python
import json
import pandas as pd
from collections import defaultdict

def preprocess_mooccube_for_colakg():
    """将MOOCCube数据转换为CoLaKG格式"""

    # 1. 加载实体数据
    with open('data/MOOCCube/entities/course.json', 'r') as f:
        courses = json.load(f)
    with open('data/MOOCCube/entities/concept.json', 'r') as f:
        concepts = json.load(f)
    with open('data/MOOCCube/entities/teacher.json', 'r') as f:
        teachers = json.load(f)

    # 2. 加载关系数据
    with open('data/MOOCCube/relations/user-course.json', 'r') as f:
        user_course = json.load(f)
    with open('data/MOOCCube/relations/course-concept.json', 'r') as f:
        course_concept = json.load(f)
    with open('data/MOOCCube/relations/teacher-course.json', 'r') as f:
        teacher_course = json.load(f)

    # 3. 构建用户-课程交互矩阵
    user_interactions = defaultdict(list)
    for interaction in user_course:
        user_id = interaction['user_id']
        course_id = interaction['course_id']
        user_interactions[user_id].append(course_id)

    # 4. 生成train.txt和test.txt
    generate_train_test_split(user_interactions)

    # 5. 构建课程知识子图
    course_subgraphs = build_course_subgraphs(courses, concepts, teachers,
                                            course_concept, teacher_course)

    # 6. 生成LLM输入文本
    generate_llm_input(course_subgraphs)

def build_course_subgraphs(courses, concepts, teachers, course_concept, teacher_course):
    """为每个课程构建知识子图"""
    course_subgraphs = {}

    for course_id, course_info in courses.items():
        subgraph_text = f"课程名称: {course_info['name']}\n"
        subgraph_text += f"课程描述: {course_info.get('description', '')}\n"

        # 添加相关概念
        related_concepts = [rel['concept_id'] for rel in course_concept
                          if rel['course_id'] == course_id]
        if related_concepts:
            concept_names = [concepts[cid]['name'] for cid in related_concepts
                           if cid in concepts]
            subgraph_text += f"相关概念: {', '.join(concept_names)}\n"

        # 添加授课教师
        course_teachers = [rel['teacher_id'] for rel in teacher_course
                         if rel['course_id'] == course_id]
        if course_teachers:
            teacher_names = [teachers[tid]['name'] for tid in course_teachers
                           if tid in teachers]
            subgraph_text += f"授课教师: {', '.join(teacher_names)}\n"

        course_subgraphs[course_id] = subgraph_text

    return course_subgraphs

def generate_llm_input(course_subgraphs):
    """生成LLM输入文件"""
    llm_input = {}

    for course_id, subgraph_text in course_subgraphs.items():
        prompt = f"课程信息如下：\n{subgraph_text}\n\n" + \
                "请基于以上信息分析该课程的特点、适合的学习者类型，以及学习该课程需要的前置知识。"
        llm_input[str(course_id)] = prompt

    with open('data/MOOCCube/llm_input_item.json', 'w', encoding='utf-8') as f:
        json.dump(llm_input, f, ensure_ascii=False, indent=2)
```

### 9.2 教育领域提示词模板

```python
# 课程推荐提示词
COURSE_SYSTEM_PROMPT = """
假设你是一位在线教育推荐专家，具有丰富的课程设计和学习路径规划经验。
你将获得一门课程的详细信息，包括课程内容、相关概念、授课教师等。
请分析该课程的以下方面：
1. 课程的核心知识点和学习目标
2. 适合学习该课程的用户画像（学历背景、专业领域、学习目的等）
3. 学习该课程所需的前置知识和技能
4. 该课程在整个学科体系中的定位和作用

请用连贯的段落形式回答，不超过200字。
"""

# 用户学习偏好分析提示词
USER_SYSTEM_PROMPT = """
假设你是一位学习行为分析专家，擅长从用户的学习历史中挖掘学习偏好和兴趣方向。
你将获得一位用户的课程学习记录，每条记录包含课程名称、相关概念、授课教师等信息。
请分析该用户的学习偏好，包括：
1. 偏好的学科领域和知识类型
2. 学习难度和深度偏好
3. 对教师风格和教学方式的偏好
4. 学习路径和进阶方向

请用连贯的段落形式总结，不超过100字。
"""
```

### 9.3 预期改进效果

通过将CoLaKG应用于MOOCCube数据集，预期可以实现：

1. **概念感知推荐**: 基于课程概念关系的智能推荐，确保学习路径的逻辑性
2. **个性化学习路径**: 根据用户历史学习行为和偏好，推荐合适难度和领域的课程
3. **先修关系建模**: 利用prerequisite-dependency关系，避免推荐超出用户能力的高难度课程
4. **多维度匹配**: 综合考虑课程内容、教师风格、学校背景等多个维度进行推荐

这种基于语义理解的推荐方法特别适合教育场景，因为它能够理解课程的深层语义和教育价值，而不仅仅是基于协同过滤的表面相似性。
