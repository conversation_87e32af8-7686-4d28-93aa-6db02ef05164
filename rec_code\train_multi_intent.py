import os
import sys
import torch
import numpy as np
from torch.utils.data import DataLoader
import time
import json
import pickle
from collections import defaultdict

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import world
from world import cprint
from mooccube_dataloader import MOOCCubeDataset
from metapath_extractor import MetaPathSubgraphExtractor
from multi_intent_llm import MultiIntentLLMProcessor, IntentEmbeddingProcessor
from multi_intent_model import MultiIntentVideoRecommender, MultiIntentTrainer
from utils import set_seed
# from Procedure import Test

def simple_evaluate(dataset, model, topks=[20, 50]):
    """简化的评估函数"""
    model.eval()

    recalls = []
    ndcgs = []

    # 随机选择100个用户进行快速评估
    test_users = np.random.choice(list(dataset.testDict.keys()),
                                 min(100, len(dataset.testDict)),
                                 replace=False)

    for user_idx in test_users:
        if user_idx not in dataset.testDict:
            continue

        ground_truth = dataset.testDict[user_idx]
        if len(ground_truth) == 0:
            continue

        # 获取用户对所有项目的评分
        user_tensor = torch.tensor([user_idx]).to(world.device)
        ratings = model.get_all_ratings(user_tensor)[0]  # [num_items]

        # 排除训练集中的项目
        train_items = dataset.allPos.get(user_idx, [])
        for item in train_items:
            ratings[item] = -float('inf')

        # 获取top-k推荐
        _, top_items = torch.topk(ratings, max(topks))
        top_items = top_items.cpu().numpy()

        # 计算指标
        for i, k in enumerate(topks):
            top_k = top_items[:k]
            hits = len(set(top_k) & set(ground_truth))

            # Recall
            recall = hits / len(ground_truth)
            if len(recalls) <= i:
                recalls.append([])
            recalls[i].append(recall)

            # NDCG
            dcg = 0
            idcg = sum([1/np.log2(j+2) for j in range(min(len(ground_truth), k))])
            for j, item in enumerate(top_k):
                if item in ground_truth:
                    dcg += 1/np.log2(j+2)
            ndcg = dcg / idcg if idcg > 0 else 0
            if len(ndcgs) <= i:
                ndcgs.append([])
            ndcgs[i].append(ndcg)

    # 计算平均值
    avg_recalls = [np.mean(r) for r in recalls]
    avg_ndcgs = [np.mean(n) for n in ndcgs]

    return {
        'recall': avg_recalls,
        'ndcg': avg_ndcgs
    }

def load_config():
    """加载配置"""
    config = {
        # 数据配置
        'dataset': 'mooccube',
        'path': '../data/MOOCCube',
        'min_interactions': 10,
        
        # 模型配置
        'latent_dim_rec': 64,
        'intent_dim': 384,
        'hidden_dim': 512,
        'final_dim': 64,
        'lightGCN_n_layers': 3,
        'dropout': 0,
        'A_split': False,
        'A_n_fold': 100,
        
        # 训练配置
        'lr': 0.001,
        'decay': 1e-4,
        'batch_size': 2048,
        'epochs': 1000,
        'early_stop': 50,
        'test_u_batch_size': 100,
        
        # 损失权重
        'bpr_weight': 1.0,
        'contrastive_weight': 0.1,
        'temperature': 0.1,
        
        # 其他配置
        'topks': [20, 50],
        'tensorboard': True,
        'comment': 'multi_intent_video_rec',
        'load': 0,
        'multicore': 0,
        'pretrain': 0,
        'seed': 2020,
        'model': 'multi_intent'
    }
    
    return config

def prepare_data_and_embeddings(config):
    """准备数据和嵌入"""
    cprint("=== 数据准备阶段 ===")
    
    # 1. 加载MOOCCube数据集
    cprint("1. 加载MOOCCube数据集...")
    dataset = MOOCCubeDataset(
        path=config['path'],
        min_interactions=config['min_interactions'],
        cache_dir="cache"
    )
    
    # 2. 提取元路径子图
    cprint("2. 提取元路径子图...")
    extractor = MetaPathSubgraphExtractor(dataset, cache_dir="cache")
    
    # 获取所有视频和用户ID
    all_video_ids = list(dataset.video_id_map.keys())
    all_user_indices = list(range(dataset.n_users))
    
    # 批量提取子图
    video_subgraphs = extractor.batch_extract_video_subgraphs(all_video_ids, max_neighbors=30)
    user_subgraphs = extractor.batch_extract_user_subgraphs(all_user_indices, max_videos=50)
    
    # 3. 转换子图为文本
    cprint("3. 转换子图为文本描述...")
    video_texts = {}
    user_texts = {}
    
    for video_id, subgraph in video_subgraphs.items():
        text = extractor.convert_subgraph_to_text(subgraph, 'video')
        if text:
            video_texts[video_id] = text
    
    for user_idx, subgraph in user_subgraphs.items():
        text = extractor.convert_subgraph_to_text(subgraph, 'user')
        if text:
            user_texts[user_idx] = text
    
    cprint(f"生成文本描述: {len(video_texts)} 个视频, {len(user_texts)} 个用户")
    
    # 4. LLM意图理解
    cprint("4. LLM多重意图理解...")
    llm_processor = MultiIntentLLMProcessor(cache_dir="cache", batch_size=10)
    
    # 估算成本
    llm_processor.estimate_api_cost(len(video_texts), len(user_texts))
    
    # 处理意图
    video_intent_texts = llm_processor.process_video_intents(video_texts)
    user_intent_texts = llm_processor.process_user_intents(user_texts)
    
    # 5. 生成嵌入向量
    cprint("5. 生成意图嵌入向量...")
    embedding_processor = IntentEmbeddingProcessor(cache_dir="cache")
    
    video_intent_embeddings = embedding_processor.process_video_intent_embeddings(video_intent_texts)
    user_intent_embeddings = embedding_processor.process_user_intent_embeddings(user_intent_texts)
    
    cprint(f"嵌入向量生成完成: {len(video_intent_embeddings)} 个视频, {len(user_intent_embeddings)} 个用户")
    
    return dataset, video_intent_embeddings, user_intent_embeddings

def create_model_and_trainer(config, dataset, video_intent_embeddings, user_intent_embeddings):
    """创建模型和训练器"""
    cprint("=== 模型创建阶段 ===")
    
    # 更新world配置
    world.config = config
    world.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    cprint(f"使用设备: {world.device}")
    
    # 创建模型
    model = MultiIntentVideoRecommender(
        config=config,
        dataset=dataset,
        video_intent_embeddings=video_intent_embeddings,
        user_intent_embeddings=user_intent_embeddings
    ).to(world.device)
    
    # 创建训练器
    trainer = MultiIntentTrainer(model, dataset, config)
    
    return model, trainer

def train_model(model, trainer, dataset, config):
    """训练模型"""
    cprint("=== 模型训练阶段 ===")
    
    # 创建数据加载器
    train_loader = DataLoader(
        dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=0
    )
    
    # 训练循环
    best_recall = 0
    patience_counter = 0
    train_losses = []
    
    for epoch in range(config['epochs']):
        start_time = time.time()
        
        # 训练一个epoch
        avg_loss, avg_bpr, avg_reg, avg_contrastive = trainer.train_one_epoch(train_loader)
        train_losses.append(avg_loss)
        
        epoch_time = time.time() - start_time
        
        cprint(f"Epoch {epoch+1}/{config['epochs']} ({epoch_time:.2f}s)")
        cprint(f"  Loss: {avg_loss:.4f} (BPR: {avg_bpr:.4f}, Reg: {avg_reg:.4f}, Contrastive: {avg_contrastive:.4f})")
        
        # 每10个epoch评估一次
        if (epoch + 1) % 10 == 0:
            cprint("开始评估...")
            model.eval()
            with torch.no_grad():
                results = simple_evaluate(dataset, model, config['topks'])
            
            # 提取recall@20作为主要指标
            recall_20 = results['recall'][0]  # 假设第一个是recall@20
            cprint(f"  Recall@20: {recall_20:.4f}")
            
            # 早停检查
            if recall_20 > best_recall:
                best_recall = recall_20
                patience_counter = 0
                # 保存最佳模型
                torch.save(model.state_dict(), 'best_multi_intent_model.pth')
                cprint(f"  新的最佳模型已保存 (Recall@20: {best_recall:.4f})")
            else:
                patience_counter += 1
                cprint(f"  性能未提升 ({patience_counter}/{config['early_stop']})")
            
            if patience_counter >= config['early_stop']:
                cprint("早停触发，训练结束")
                break
    
    return model, train_losses

def evaluate_model(model, dataset, config):
    """评估模型"""
    cprint("=== 模型评估阶段 ===")
    
    # 加载最佳模型
    if os.path.exists('best_multi_intent_model.pth'):
        model.load_state_dict(torch.load('best_multi_intent_model.pth'))
        cprint("已加载最佳模型")
    
    model.eval()
    with torch.no_grad():
        results = simple_evaluate(dataset, model, config['topks'])
    
    cprint("最终评估结果:")
    for metric, values in results.items():
        if isinstance(values, list):
            for i, topk in enumerate(config['topks']):
                cprint(f"  {metric}@{topk}: {values[i]:.4f}")
        else:
            cprint(f"  {metric}: {values:.4f}")
    
    return results

def save_training_info(config, results, train_losses):
    """保存训练信息"""
    training_info = {
        'config': config,
        'final_results': results,
        'train_losses': train_losses,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    with open('multi_intent_training_info.json', 'w', encoding='utf-8') as f:
        json.dump(training_info, f, ensure_ascii=False, indent=2)
    
    cprint("训练信息已保存到 multi_intent_training_info.json")

def main():
    """主函数"""
    cprint("开始多重意图视频推荐系统训练")
    
    # 加载配置
    config = load_config()
    
    # 设置随机种子
    set_seed(config['seed'])
    
    try:
        # 1. 准备数据和嵌入
        dataset, video_intent_embeddings, user_intent_embeddings = prepare_data_and_embeddings(config)
        
        # 2. 创建模型和训练器
        model, trainer = create_model_and_trainer(config, dataset, video_intent_embeddings, user_intent_embeddings)
        
        # 3. 训练模型
        model, train_losses = train_model(model, trainer, dataset, config)
        
        # 4. 评估模型
        results = evaluate_model(model, dataset, config)
        
        # 5. 保存训练信息
        save_training_info(config, results, train_losses)
        
        cprint("训练完成！")
        
    except Exception as e:
        cprint(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
