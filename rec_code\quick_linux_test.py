#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Linux环境快速测试脚本
"""

import os
import sys
import torch
import numpy as np
import time

def test_environment():
    """测试环境"""
    print("=" * 60)
    print("Linux环境测试")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"数据目录存在: {os.path.exists('../data/MOOCCube')}")
    
    return True

def test_data_loading():
    """测试数据加载"""
    print("\n📊 测试数据加载...")
    
    try:
        from mooccube_dataloader import MOOCCubeDataset
        
        # 使用较小的参数进行快速测试
        dataset = MOOCCubeDataset(
            path="../data/MOOCCube",
            min_interactions=5,  # 降低阈值
            cache_dir="test_cache_linux"
        )
        
        print(f"✅ 数据加载成功")
        print(f"   用户数: {dataset.n_users:,}")
        print(f"   视频数: {dataset.m_items:,}")
        print(f"   训练交互: {dataset.trainDataSize:,}")
        
        # 测试采样
        users, pos_items, neg_items = dataset.sample(batch_size=32)
        print(f"✅ 数据采样成功: {len(users)} 个样本")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n🤖 测试模型创建...")
    
    try:
        # 创建小规模测试数据
        class MiniDataset:
            def __init__(self):
                self.n_users = 100
                self.m_items = 50
                self.trainDataSize = 1000
                self.testDataSize = 200

                # 必需的属性
                self.user_id_map = {f"U_{i}": i for i in range(self.n_users)}
                self.video_id_map = {f"V_{i}": i for i in range(self.m_items)}
                self.idx_to_user = {i: f"U_{i}" for i in range(self.n_users)}
                self.idx_to_video = {i: f"V_{i}" for i in range(self.m_items)}
                self.knowledge_graph = {'video_concepts': {}, 'course_videos': {}, 'teacher_courses': {}, 'prerequisite_deps': {}}

                # 创建模拟的稀疏图
                self.Graph = self._create_sparse_graph()

            def _create_sparse_graph(self):
                """创建用户-物品二部图"""
                # 创建随机的用户-物品交互
                num_edges = min(self.trainDataSize, 500)  # 限制边数
                user_indices = np.random.randint(0, self.n_users, num_edges)
                item_indices = np.random.randint(0, self.m_items, num_edges)

                # 构建邻接矩阵的索引
                # 用户节点: 0 到 n_users-1
                # 物品节点: n_users 到 n_users+m_items-1
                row_indices = []
                col_indices = []

                # 用户到物品的边
                for u, i in zip(user_indices, item_indices):
                    row_indices.extend([u, self.n_users + i])
                    col_indices.extend([self.n_users + i, u])

                # 创建稀疏图
                indices = torch.LongTensor([row_indices, col_indices])
                values = torch.ones(len(row_indices), dtype=torch.float32)
                shape = (self.n_users + self.m_items, self.n_users + self.m_items)

                return torch.sparse_coo_tensor(indices, values, shape)

            def getSparseGraph(self):
                """返回稀疏图"""
                return self.Graph
        
        dataset = MiniDataset()
        
        from multi_intent_model import MultiIntentVideoRecommender
        
        config = {
            'latent_dim_rec': 32,
            'intent_dim': 64,
            'hidden_dim': 128,
            'final_dim': 32,
            'lightGCN_n_layers': 2,
            'dropout': 0.0,
            'A_split': False,
            'pretrain': 0,
            'bpr_weight': 1.0,
            'contrastive_weight': 0.1,
            'temperature': 0.1
        }
        
        video_intent_embeddings = np.random.randn(dataset.m_items, 64).astype(np.float32)
        user_intent_embeddings = np.random.randn(dataset.n_users, 64).astype(np.float32)
        
        model = MultiIntentVideoRecommender(
            config=config,
            dataset=dataset,
            video_intent_embeddings=video_intent_embeddings,
            user_intent_embeddings=user_intent_embeddings
        )
        
        print(f"✅ 模型创建成功")
        print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试前向传播
        users = torch.randint(0, dataset.n_users, (16,))
        pos_items = torch.randint(0, dataset.m_items, (16,))
        neg_items = torch.randint(0, dataset.m_items, (16,))
        
        bpr_loss, reg_loss, contrastive_loss = model.bpr_loss(users, pos_items, neg_items)
        print(f"✅ 前向传播成功")
        print(f"   BPR损失: {bpr_loss.item():.4f}")
        print(f"   正则化损失: {reg_loss.item():.4f}")
        print(f"   对比损失: {contrastive_loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始Linux环境快速测试...")
    
    # 1. 测试环境
    if not test_environment():
        return False
    
    # 2. 测试模型创建
    if not test_model_creation():
        return False
    
    # 3. 测试数据加载（可选，因为比较耗时）
    print("\n是否测试完整数据加载？(y/n): ", end="")
    try:
        choice = input().strip().lower()
        if choice == 'y':
            if not test_data_loading():
                return False
        else:
            print("跳过完整数据加载测试")
    except:
        print("跳过完整数据加载测试")
    
    print(f"\n🎉 所有测试通过！")
    print(f"💡 环境准备就绪，可以开始训练")
    print(f"🚀 建议运行: bash train_simple.sh")
    
    # 清理测试缓存
    import shutil
    if os.path.exists("test_cache_linux"):
        try:
            shutil.rmtree("test_cache_linux")
            print("🧹 清理测试缓存完成")
        except:
            pass
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
