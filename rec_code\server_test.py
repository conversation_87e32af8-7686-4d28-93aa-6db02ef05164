#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器环境测试脚本
验证多重意图视频推荐系统在Linux服务器上的运行环境
"""

import os
import sys
import torch
import numpy as np

def test_environment():
    """测试基础环境"""
    print("=== 环境测试 ===")
    
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 测试必要的包
    required_packages = [
        'numpy', 'scipy', 'pandas', 'sklearn', 
        'transformers', 'sentence_transformers'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装的包: {missing_packages}")
        print("安装命令: pip install " + " ".join(missing_packages))
        return False
    
    return True

def test_data_structure():
    """测试数据结构"""
    print("\n=== 数据结构测试 ===")
    
    data_path = "../data/MOOCCube"
    if not os.path.exists(data_path):
        print(f"✗ 数据目录不存在: {data_path}")
        return False
    
    # 检查关键文件
    required_files = [
        "entities/user.json",
        "entities/video.json", 
        "entities/concept.json",
        "entities/course.json",
        "relations/user-video.json",
        "relations/video-concept.json",
        "relations/course-video.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(data_path, file_path)
        if os.path.exists(full_path):
            size = os.path.getsize(full_path) / (1024*1024)  # MB
            print(f"✓ {file_path} ({size:.1f}MB)")
        else:
            print(f"✗ {file_path}")
            missing_files.append(file_path)
    
    # 检查alternative文件
    alt_files = [
        "additional_information/user_video_act.json",
        "entities/teacher.json",
        "relations/video-teacher.json",
        "relations/prerequisite.json"
    ]
    
    print("\n可选文件:")
    for file_path in alt_files:
        full_path = os.path.join(data_path, file_path)
        if os.path.exists(full_path):
            size = os.path.getsize(full_path) / (1024*1024)  # MB
            print(f"✓ {file_path} ({size:.1f}MB)")
        else:
            print(f"- {file_path} (可选)")
    
    if missing_files:
        print(f"\n缺失关键文件: {missing_files}")
        return False
    
    return True

def test_imports():
    """测试模块导入"""
    print("\n=== 模块导入测试 ===")
    
    modules = [
        ('world', 'world'),
        ('utils', 'utils'),
        ('dataloader', 'dataloader'),
        ('model', 'model'),
        ('mooccube_dataloader', 'mooccube_dataloader'),
        ('metapath_extractor', 'metapath_extractor'),
        ('multi_intent_llm', 'multi_intent_llm'),
        ('multi_intent_model', 'multi_intent_model')
    ]
    
    failed_imports = []
    for module_name, file_name in modules:
        try:
            __import__(module_name)
            print(f"✓ {module_name}")
        except ImportError as e:
            print(f"✗ {module_name}: {e}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"\n导入失败的模块: {failed_imports}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 基本功能测试 ===")
    
    try:
        # 测试数据加载
        from mooccube_dataloader import MOOCCubeDataset
        
        print("测试数据加载...")
        dataset = MOOCCubeDataset(
            path="../data/MOOCCube",
            min_interactions=5,
            cache_dir="test_cache"
        )
        
        print(f"✓ 数据加载成功")
        print(f"  用户数: {dataset.n_users}")
        print(f"  视频数: {dataset.m_items}")
        print(f"  训练交互数: {dataset.trainDataSize}")
        
        # 测试模型创建
        from multi_intent_model import MultiIntentVideoRecommender
        
        print("测试模型创建...")
        
        # 创建模拟嵌入
        video_intent_embeddings = {}
        user_intent_embeddings = {}
        
        for i in range(min(10, dataset.m_items)):
            video_id = dataset.idx_to_video.get(i, f"video_{i}")
            video_intent_embeddings[video_id] = np.random.randn(384).tolist()
        
        for i in range(min(10, dataset.n_users)):
            user_intent_embeddings[i] = np.random.randn(384).tolist()
        
        config = {
            'latent_dim_rec': 64,
            'intent_dim': 384,
            'hidden_dim': 512,
            'final_dim': 64,
            'lightGCN_n_layers': 3,
            'dropout': 0,
            'A_split': False,
            'A_n_fold': 100,
            'temperature': 0.1
        }
        
        model = MultiIntentVideoRecommender(
            config=config,
            dataset=dataset,
            video_intent_embeddings=video_intent_embeddings,
            user_intent_embeddings=user_intent_embeddings
        )
        
        print(f"✓ 模型创建成功")
        print(f"  参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试前向传播
        print("测试前向传播...")
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        
        batch_size = 4
        users = torch.randint(0, dataset.n_users, (batch_size,)).to(device)
        pos_items = torch.randint(0, dataset.m_items, (batch_size,)).to(device)
        neg_items = torch.randint(0, dataset.m_items, (batch_size,)).to(device)
        
        with torch.no_grad():
            users_emb, pos_emb, neg_emb, contrastive_loss = model(users, pos_items, neg_items)
            print(f"✓ 前向传播成功")
            print(f"  对比损失: {contrastive_loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("多重意图视频推荐系统 - 服务器环境测试")
    print("=" * 60)
    
    # 创建测试目录
    os.makedirs("test_cache", exist_ok=True)
    
    tests = [
        ("环境检查", test_environment),
        ("数据结构", test_data_structure),
        ("模块导入", test_imports),
        ("基本功能", test_basic_functionality)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"{test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结:")
    for test_name, success in results.items():
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    passed = sum(results.values())
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 服务器环境测试通过!")
        print("可以开始训练:")
        print("  python train_multi_intent.py --help  # 查看参数")
        print("  bash train_mooccube.sh              # 开始训练")
    else:
        print("\n⚠️  部分测试失败，请检查环境配置")
    
    # 清理测试缓存
    import shutil
    if os.path.exists("test_cache"):
        shutil.rmtree("test_cache")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
