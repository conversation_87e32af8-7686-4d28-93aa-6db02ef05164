from transformers import AutoTokenizer, AutoModel
import torch
import json

# 指定 LLM 输出的 JSON 文件路径
text_answer_path = "data/mind/llm_response_item.json"  # the file path of the LLM's textual output.
# 指定保存嵌入向量的文件路径
text_embedding_path_to_write = "data/mind/mind_embeddings_simcse_kg.pt" # generating the text embeddings w.r.t. LLM's textual output.
# 检测是否可用 GPU，若可用则使用 CUDA，否则使用 CPU
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
# 加载 sup-simcse-roberta-large 的分词器，用于将文本转换为输入张量
tokenizer = AutoTokenizer.from_pretrained("princeton-nlp/sup-simcse-roberta-large")
# 加载对应的预训练模型
model = AutoModel.from_pretrained("princeton-nlp/sup-simcse-roberta-large")

with open(text_answer_path, 'r') as file:
    # item_txt_dic：字典，键为项目 ID（例如 0, 1...），值为 LLM 响应文本
    item_txt_dic = json.load(file)

# item_num：唯一项目的数量，使用 set 去重
item_num = len(set(item_txt_dic.keys()))
# texts：列表，包含按索引排序的文本，假设键为连续整数
texts = [item_txt_dic[str(i)] for i in range(item_num)]
batch_size = 64  # 每次处理的文本数量
all_embeddings = []  # 存储所有批次的嵌入
model.to(device)
# 按 batch_size 分批处理 texts，打印当前索引以跟踪进度
for i in range(0, len(texts), batch_size):
    print(i)
    batch_texts = texts[i:i + batch_size]
    # tokenizer(batch_texts, ...)：将批文本分词 padding=True 填充到相同长度 truncation=True 截断超长序列
    inputs_simcse = tokenizer(batch_texts, padding=True, truncation=True, return_tensors="pt")
    # inputs_simcse：字典
    inputs_simcse = {key: value.to(device) for key, value in inputs_simcse.items()}
    
    with torch.no_grad():
        # 调用模型，output_hidden_states=True 返回所有隐藏层状态，return_dict=True 以字典形式返回
        embeddings_simcse = model(**inputs_simcse, output_hidden_states=True, return_dict=True).pooler_output
    # 将嵌入移回 CPU，方便后续拼接
    all_embeddings.append(embeddings_simcse.cpu())

# 沿维度 0 拼接所有批次嵌入，形成完整的嵌入张量
all_embeddings = torch.cat(all_embeddings, dim=0)
# 将张量保存为 .pt 文件，供 rec_code 使用
torch.save(all_embeddings, text_embedding_path_to_write)
print("inference over", all_embeddings.shape)
