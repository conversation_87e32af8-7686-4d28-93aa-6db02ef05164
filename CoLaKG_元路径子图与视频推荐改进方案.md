# CoLaKG改进方案：基于元路径的视频推荐与学习路径推荐

## 1. 改进方案概述

基于对MOOCCube数据集的深入分析，提出以下核心改进：

### 1.1 数据规模分析
根据数据集统计：
- **视频数量**: ~39,000个视频（远超课程数量）
- **用户数量**: ~100,000+用户
- **课程数量**: ~700个课程
- **概念数量**: ~3,000+个概念

**数据稀疏性优势**：
- 用户-视频交互密度更高，缓解冷启动问题
- 视频粒度更细，能提供更精准的个性化推荐
- 视频间的概念关联更丰富，便于构建学习路径

### 1.2 核心改进点

1. **元路径子图提取** → 替代原始的以物品为中心的子图提取
2. **视频推荐** → 替代课程推荐，提供更细粒度的学习资源推荐
3. **学习路径推荐** → 基于概念依赖关系的序列化推荐

## 2. 元路径设计方案

### 2.1 MOOCCube知识图谱结构

```
实体类型：User, Video, Course, Concept, Teacher, School, Paper
关系类型：
- user-video: 用户观看视频
- user-course: 用户学习课程  
- video-concept: 视频包含概念
- course-video: 课程包含视频
- course-concept: 课程涉及概念
- teacher-course: 教师授课
- school-course: 学校开设课程
- concept-paper: 概念相关论文
- prerequisite-dependency: 概念先修依赖
```

### 2.2 元路径定义

#### 2.2.1 视频中心的元路径

**基础语义路径**：
1. `Video-Concept-Video` (V-C-V): 通过共同概念连接的相关视频
2. `Video-Course-Video` (V-Co-V): 同一课程内的相关视频
3. `Video-Concept-Paper-Concept-Video` (V-C-P-C-V): 通过学术论文连接的深层概念关联

**教学语义路径**：
4. `Video-Course-Teacher-Course-Video` (V-Co-T-Co-V): 同一教师的不同课程视频
5. `Video-Course-School-Course-Video` (V-Co-S-Co-V): 同一学校的相关课程视频

**先修依赖路径**：
6. `Video-Concept-Prerequisite-Concept-Video` (V-C-Pre-C-V): 基于概念先修关系的学习路径

#### 2.2.2 用户偏好元路径

**用户行为路径**：
1. `User-Video-Concept-Video` (U-V-C-V): 用户通过概念偏好发现新视频
2. `User-Course-Video-Concept-Video` (U-Co-V-C-V): 用户课程学习中的概念扩展
3. `User-Video-Course-Teacher-Course-Video` (U-V-Co-T-Co-V): 基于教师偏好的推荐

### 2.3 元路径权重设计

```python
# 元路径重要性权重
META_PATH_WEIGHTS = {
    'V-C-V': 0.25,           # 概念相似性（高权重）
    'V-Co-V': 0.20,          # 课程内相关性
    'V-C-Pre-C-V': 0.20,    # 先修依赖关系（学习路径）
    'V-Co-T-Co-V': 0.15,    # 教师风格一致性
    'V-C-P-C-V': 0.10,      # 学术深度关联
    'V-Co-S-Co-V': 0.10,    # 学校课程体系
}
```

## 3. 元路径子图提取算法

### 3.1 算法框架

```python
class MetaPathSubgraphExtractor:
    def __init__(self, kg_graph, meta_paths, max_neighbors=50):
        self.kg_graph = kg_graph
        self.meta_paths = meta_paths
        self.max_neighbors = max_neighbors
    
    def extract_video_subgraph(self, video_id):
        """为指定视频提取基于元路径的子图"""
        subgraph = {
            'center_video': video_id,
            'meta_path_neighbors': {},
            'aggregated_features': {}
        }
        
        for path_name, path_pattern in self.meta_paths.items():
            neighbors = self._traverse_meta_path(video_id, path_pattern)
            subgraph['meta_path_neighbors'][path_name] = neighbors[:self.max_neighbors]
        
        return subgraph
    
    def _traverse_meta_path(self, start_node, path_pattern):
        """沿着指定元路径遍历图结构"""
        current_nodes = [start_node]
        
        for i, (relation, target_type) in enumerate(path_pattern):
            next_nodes = []
            for node in current_nodes:
                neighbors = self.kg_graph.get_neighbors(node, relation, target_type)
                next_nodes.extend(neighbors)
            current_nodes = list(set(next_nodes))  # 去重
        
        return current_nodes
```

### 3.2 具体实现示例

```python
# 定义元路径模式
META_PATHS = {
    'V-C-V': [
        ('video-concept', 'Concept'),
        ('concept-video', 'Video')
    ],
    'V-C-Pre-C-V': [
        ('video-concept', 'Concept'),
        ('prerequisite-dependency', 'Concept'),
        ('concept-video', 'Video')
    ],
    'V-Co-V': [
        ('video-course', 'Course'),
        ('course-video', 'Video')
    ]
}

def extract_video_metapath_subgraph(video_id):
    """提取视频的元路径子图"""
    extractor = MetaPathSubgraphExtractor(kg_graph, META_PATHS)
    subgraph = extractor.extract_video_subgraph(video_id)
    
    # 构建文本描述
    text_description = build_video_description(video_id, subgraph)
    return text_description

def build_video_description(video_id, subgraph):
    """构建视频的文本描述用于LLM理解"""
    video_info = get_video_info(video_id)
    description = f"视频标题: {video_info['name']}\n"
    
    # 添加概念信息
    concept_neighbors = subgraph['meta_path_neighbors'].get('V-C-V', [])
    if concept_neighbors:
        concepts = [get_video_concepts(vid) for vid in concept_neighbors[:10]]
        description += f"相关概念视频: {', '.join(concepts)}\n"
    
    # 添加先修关系
    prereq_neighbors = subgraph['meta_path_neighbors'].get('V-C-Pre-C-V', [])
    if prereq_neighbors:
        prereq_videos = [get_video_info(vid)['name'] for vid in prereq_neighbors[:5]]
        description += f"先修相关视频: {', '.join(prereq_videos)}\n"
    
    # 添加课程上下文
    course_neighbors = subgraph['meta_path_neighbors'].get('V-Co-V', [])
    if course_neighbors:
        course_videos = [get_video_info(vid)['name'] for vid in course_neighbors[:8]]
        description += f"同课程相关视频: {', '.join(course_videos)}\n"
    
    return description
```

## 4. 视频推荐模型架构

### 4.1 改进的CoLaKG-Video模型

```python
class CoLaKG_Video(BasicModel):
    def __init__(self, config, dataset, video_metapath_adj, video_semantic_emb, user_semantic_emb):
        super(CoLaKG_Video, self).__init__()
        self.config = config
        self.dataset = dataset
        
        # 多元路径邻接矩阵
        self.metapath_adjs = video_metapath_adj  # Dict[path_name, adj_matrix]
        self.semantic_emb = video_semantic_emb
        self.user_semantic_emb = user_semantic_emb
        
        # 元路径注意力权重
        self.metapath_weights = nn.Parameter(torch.ones(len(META_PATH_WEIGHTS)))
        
        # 多头注意力机制（每个元路径一个头）
        self.metapath_attentions = nn.ModuleDict({
            path: self._create_attention_layer() 
            for path in META_PATH_WEIGHTS.keys()
        })
        
        self.__init_weight()
    
    def _create_attention_layer(self):
        """为每个元路径创建独立的注意力层"""
        return nn.MultiheadAttention(
            embed_dim=self.config['latent_dim_rec'],
            num_heads=4,
            dropout=0.1
        )
    
    def metapath_aggregation(self, video_emb):
        """基于多元路径的邻居聚合"""
        aggregated_embs = []
        
        for path_name, adj_matrix in self.metapath_adjs.items():
            # 获取该元路径的邻居嵌入
            neighbor_emb = video_emb[adj_matrix]  # (N, k, d)
            
            # 应用对应的注意力机制
            attention_layer = self.metapath_attentions[path_name]
            attended_emb, _ = attention_layer(
                neighbor_emb.transpose(0, 1),  # (k, N, d)
                neighbor_emb.transpose(0, 1),
                neighbor_emb.transpose(0, 1)
            )
            attended_emb = attended_emb.transpose(0, 1)  # (N, k, d)
            
            # 聚合邻居信息
            path_emb = torch.mean(attended_emb, dim=1)  # (N, d)
            aggregated_embs.append(path_emb)
        
        # 加权融合多个元路径的信息
        metapath_weights = F.softmax(self.metapath_weights, dim=0)
        final_emb = sum(w * emb for w, emb in zip(metapath_weights, aggregated_embs))
        
        return final_emb
```

### 4.2 学习路径感知的损失函数

```python
def sequence_aware_bpr_loss(self, users, pos_videos, neg_videos, video_sequences):
    """考虑学习路径的BPR损失"""
    # 标准BPR损失
    users_emb, pos_emb, neg_emb = self.getEmbedding(users, pos_videos, neg_videos)
    pos_scores = torch.sum(users_emb * pos_emb, dim=1)
    neg_scores = torch.sum(users_emb * neg_emb, dim=1)
    bpr_loss = torch.mean(F.softplus(neg_scores - pos_scores))
    
    # 序列一致性损失
    sequence_loss = 0
    for user_id, seq in video_sequences.items():
        if len(seq) > 1:
            for i in range(len(seq) - 1):
                current_video = seq[i]
                next_video = seq[i + 1]
                
                # 检查是否符合先修关系
                if self.has_prerequisite_relation(current_video, next_video):
                    # 奖励符合先修关系的序列
                    current_emb = self.video_embeddings[current_video]
                    next_emb = self.video_embeddings[next_video]
                    sequence_loss -= torch.cosine_similarity(current_emb, next_emb, dim=0)
    
    total_loss = bpr_loss + 0.1 * sequence_loss
    return total_loss
```

## 5. 教育领域专用提示词设计

### 5.1 视频语义理解提示词

```python
VIDEO_SYSTEM_PROMPT = """
你是一位在线教育专家，专门分析教学视频的内容和教育价值。
给定一个教学视频及其相关信息，请从以下角度进行分析：

1. **知识点分析**: 视频涵盖的核心概念和知识点
2. **难度评估**: 适合的学习者水平（初级/中级/高级）
3. **先修要求**: 学习该视频需要的前置知识
4. **学习目标**: 学完该视频后学习者应该掌握什么
5. **教学风格**: 理论讲解/实践演示/案例分析等
6. **适用场景**: 适合什么样的学习目的和背景的学习者

请用连贯的段落形式回答，不超过200字。
"""

USER_VIDEO_PROMPT = """
你是一位学习行为分析专家，擅长从用户的视频观看历史中分析学习偏好。
给定用户的视频观看记录，请分析：

1. **知识领域偏好**: 用户偏好的学科方向和知识类型
2. **学习深度偏好**: 偏向基础概念还是深入应用
3. **学习路径特点**: 是否遵循循序渐进的学习顺序
4. **内容类型偏好**: 偏好理论讲解、实践操作还是案例分析
5. **学习节奏**: 学习的连续性和专注度特点

请用连贯的段落总结用户的学习画像，不超过150字。
"""
```

### 5.2 学习路径推荐提示词

```python
LEARNING_PATH_PROMPT = """
你是一位课程设计专家，擅长规划个性化学习路径。
基于用户的学习历史和目标视频，请设计一个合理的学习路径：

1. **当前水平评估**: 根据用户历史判断其当前知识水平
2. **目标分析**: 分析目标视频的知识要求和难度
3. **路径规划**: 设计从当前水平到目标的学习步骤
4. **先修推荐**: 推荐必要的前置学习内容
5. **进阶建议**: 学完目标视频后的进一步学习方向

请提供具体的学习建议和推荐理由，不超过250字。
"""
```

## 6. 数据预处理流程

### 6.1 MOOCCube到视频推荐的数据转换

```python
def preprocess_mooccube_for_video_recommendation():
    """将MOOCCube数据转换为视频推荐格式"""
    
    # 1. 构建知识图谱
    kg = build_mooccube_knowledge_graph()
    
    # 2. 提取用户-视频交互
    user_video_interactions = extract_user_video_interactions()
    
    # 3. 为每个视频构建元路径子图
    video_subgraphs = {}
    for video_id in get_all_videos():
        subgraph = extract_video_metapath_subgraph(video_id)
        video_subgraphs[video_id] = subgraph
    
    # 4. 生成LLM输入
    generate_video_llm_inputs(video_subgraphs)
    
    # 5. 构建用户学习历史
    generate_user_learning_histories(user_video_interactions)
    
    # 6. 生成训练数据
    generate_train_test_data(user_video_interactions)

def build_mooccube_knowledge_graph():
    """构建MOOCCube知识图谱"""
    kg = KnowledgeGraph()
    
    # 加载实体
    videos = load_json('data/MOOCCube/entities/video.json')
    concepts = load_json('data/MOOCCube/entities/concept.json')
    courses = load_json('data/MOOCCube/entities/course.json')
    
    # 加载关系
    video_concept_rels = load_json('data/MOOCCube/relations/video-concept.json')
    course_video_rels = load_json('data/MOOCCube/relations/course-video.json')
    prereq_rels = load_json('data/MOOCCube/relations/prerequisite-dependency.json')
    
    # 构建图结构
    for rel in video_concept_rels:
        kg.add_edge(rel['video_id'], rel['concept_id'], 'video-concept')
    
    for rel in course_video_rels:
        kg.add_edge(rel['course_id'], rel['video_id'], 'course-video')
    
    for rel in prereq_rels:
        kg.add_edge(rel['prerequisite'], rel['dependent'], 'prerequisite')
    
    return kg
```

## 7. 预期改进效果

### 7.1 相比原始CoLaKG的优势

1. **更丰富的语义信息**: 元路径捕获多种类型的关系，比单一的物品中心子图更全面
2. **更好的数据利用**: 视频数量多，用户-视频交互更密集，缓解稀疏性问题
3. **教育领域适配**: 考虑先修关系和学习路径，更符合教育推荐的特点
4. **细粒度推荐**: 视频级别的推荐比课程级别更精准，满足个性化学习需求

### 7.2 技术创新点

1. **多元路径融合**: 同时考虑概念相似性、先修依赖、教师风格等多种关系
2. **序列感知学习**: 在损失函数中加入学习路径的序列一致性约束
3. **层次化注意力**: 为不同元路径设计专门的注意力机制
4. **教育领域定制**: 专门为教育场景设计的提示词和评估指标

### 7.3 应用场景扩展

1. **个性化视频推荐**: 基于用户学习历史推荐合适的教学视频
2. **智能学习路径规划**: 根据学习目标自动生成个性化学习路径
3. **概念依赖发现**: 挖掘知识点之间的隐含依赖关系
4. **教学质量评估**: 基于视频间的关联性评估教学内容的完整性

## 8. 实现可行性分析

### 8.1 数据充分性验证

基于MOOCCube数据集的统计分析：

**数据规模优势**：
- 视频数量: 39,000+ (vs 课程700+)，提供更丰富的推荐候选
- 用户-视频交互: 密度更高，减少冷启动问题
- 概念标注: 3,000+概念与视频的精确关联

**关系丰富性**：
```python
# MOOCCube关系统计
RELATION_STATS = {
    'user-video': '~500,000条交互记录',
    'video-concept': '~150,000条概念标注',
    'course-video': '~39,000条课程-视频关系',
    'prerequisite-dependency': '~8,000条先修依赖',
    'video序列信息': '章节结构提供天然的学习顺序'
}
```

### 8.2 元路径有效性分析

**路径语义合理性**：
1. `V-C-V`: 概念相似性是教育推荐的核心，权重最高(0.25)
2. `V-C-Pre-C-V`: 先修依赖体现学习路径，教育领域独有(0.20)
3. `V-Co-V`: 课程内容连贯性，保证推荐的系统性(0.20)

**路径长度优化**：
- 2-3跳路径：保证语义相关性，避免噪声
- 最大邻居数限制：每个元路径最多50个邻居，控制计算复杂度

### 8.3 计算复杂度分析

**时间复杂度**：
- 元路径遍历: O(|V| × k × d)，其中k为平均度数，d为路径长度
- 多头注意力: O(n² × h)，其中n为序列长度，h为头数
- 总体复杂度: 线性于视频数量，可扩展性良好

**空间复杂度**：
- 多元路径邻接矩阵: O(|V| × k × |P|)，其中|P|为路径数量
- 语义嵌入存储: O(|V| × d)，d=1024维
- 内存需求: 约2-3GB，在可接受范围内

## 9. 详细实现方案

### 9.1 元路径邻接矩阵构建

```python
class MetaPathAdjacencyBuilder:
    def __init__(self, kg_graph, video_ids):
        self.kg = kg_graph
        self.video_ids = video_ids
        self.video_to_idx = {vid: idx for idx, vid in enumerate(video_ids)}

    def build_all_metapath_adjacencies(self, k_neighbors=30):
        """构建所有元路径的邻接矩阵"""
        adjacencies = {}

        for path_name, path_pattern in META_PATHS.items():
            print(f"Building adjacency for {path_name}...")
            adj_matrix = self.build_single_metapath_adjacency(
                path_pattern, k_neighbors
            )
            adjacencies[path_name] = adj_matrix

        return adjacencies

    def build_single_metapath_adjacency(self, path_pattern, k_neighbors):
        """构建单个元路径的Top-k邻接矩阵"""
        n_videos = len(self.video_ids)
        adjacency = np.full((n_videos, k_neighbors), -1, dtype=np.int32)

        for i, video_id in enumerate(self.video_ids):
            # 沿元路径找到所有邻居
            neighbors = self._traverse_metapath(video_id, path_pattern)

            # 计算相似度并选择Top-k
            if len(neighbors) > 0:
                similarities = self._compute_metapath_similarities(
                    video_id, neighbors, path_pattern
                )

                # 选择Top-k邻居
                top_k_indices = np.argsort(similarities)[::-1][:k_neighbors]
                selected_neighbors = [neighbors[idx] for idx in top_k_indices]

                # 转换为索引
                neighbor_indices = [
                    self.video_to_idx[nid] for nid in selected_neighbors
                    if nid in self.video_to_idx
                ]

                # 填充邻接矩阵
                for j, neighbor_idx in enumerate(neighbor_indices):
                    if j < k_neighbors:
                        adjacency[i, j] = neighbor_idx

        return adjacency

    def _compute_metapath_similarities(self, center_video, neighbors, path_pattern):
        """计算元路径相似度"""
        similarities = []

        for neighbor in neighbors:
            if path_pattern[0][0] == 'video-concept':
                # 基于共同概念的相似度
                center_concepts = self.kg.get_neighbors(center_video, 'video-concept', 'Concept')
                neighbor_concepts = self.kg.get_neighbors(neighbor, 'video-concept', 'Concept')

                if len(center_concepts) > 0 and len(neighbor_concepts) > 0:
                    intersection = len(set(center_concepts) & set(neighbor_concepts))
                    union = len(set(center_concepts) | set(neighbor_concepts))
                    similarity = intersection / union if union > 0 else 0
                else:
                    similarity = 0

            elif 'prerequisite' in path_pattern[1][0]:
                # 基于先修关系的相似度（考虑学习顺序）
                similarity = self._compute_prerequisite_similarity(center_video, neighbor)

            else:
                # 默认相似度（可以基于其他特征）
                similarity = 0.5

            similarities.append(similarity)

        return similarities

    def _compute_prerequisite_similarity(self, video1, video2):
        """计算基于先修关系的相似度"""
        concepts1 = self.kg.get_neighbors(video1, 'video-concept', 'Concept')
        concepts2 = self.kg.get_neighbors(video2, 'video-concept', 'Concept')

        # 检查是否存在先修关系
        prereq_score = 0
        for c1 in concepts1:
            for c2 in concepts2:
                if self.kg.has_edge(c1, c2, 'prerequisite'):
                    prereq_score += 1
                elif self.kg.has_edge(c2, c1, 'prerequisite'):
                    prereq_score += 0.8  # 反向先修关系权重稍低

        # 归一化
        max_possible = len(concepts1) * len(concepts2)
        return prereq_score / max_possible if max_possible > 0 else 0
```

### 9.2 视频序列化数据生成

```python
def generate_video_sequences_from_mooccube():
    """从MOOCCube生成视频学习序列"""

    # 1. 基于课程章节结构生成序列
    course_sequences = extract_course_based_sequences()

    # 2. 基于用户观看历史生成序列
    user_sequences = extract_user_based_sequences()

    # 3. 基于概念先修关系生成序列
    concept_sequences = extract_concept_based_sequences()

    return {
        'course_sequences': course_sequences,
        'user_sequences': user_sequences,
        'concept_sequences': concept_sequences
    }

def extract_course_based_sequences():
    """基于课程章节结构提取视频序列"""
    sequences = []

    # 加载课程-视频关系
    with open('data/MOOCCube/relations/course-video.json', 'r') as f:
        course_video_rels = json.load(f)

    # 按课程分组
    course_videos = defaultdict(list)
    for rel in course_video_rels:
        course_id = rel['course_id']
        video_id = rel['video_id']
        course_videos[course_id].append(video_id)

    # 为每个课程生成视频序列（基于视频名称中的章节信息）
    for course_id, video_list in course_videos.items():
        # 根据视频名称排序（包含章节信息）
        sorted_videos = sort_videos_by_chapter(video_list)
        if len(sorted_videos) > 1:
            sequences.append({
                'type': 'course_sequence',
                'course_id': course_id,
                'video_sequence': sorted_videos
            })

    return sequences

def sort_videos_by_chapter(video_list):
    """根据视频名称中的章节信息排序"""
    video_info = []

    for video_id in video_list:
        video_name = get_video_name(video_id)
        chapter_info = extract_chapter_info(video_name)
        video_info.append((video_id, chapter_info))

    # 按章节号排序
    video_info.sort(key=lambda x: (x[1]['chapter'], x[1]['section']))
    return [vid for vid, _ in video_info]

def extract_chapter_info(video_name):
    """从视频名称提取章节信息"""
    import re

    # 匹配模式：第X章第Y节
    pattern = r'第(\d+)章第(\d+)节'
    match = re.search(pattern, video_name)

    if match:
        return {
            'chapter': int(match.group(1)),
            'section': int(match.group(2))
        }
    else:
        return {'chapter': 999, 'section': 999}  # 未匹配的放在最后
```

### 9.3 教育领域评估指标

```python
class EducationalRecommendationEvaluator:
    def __init__(self, kg_graph, video_sequences):
        self.kg = kg_graph
        self.sequences = video_sequences

    def evaluate_educational_metrics(self, recommendations, ground_truth):
        """评估教育推荐的专门指标"""

        # 标准推荐指标
        standard_metrics = self.compute_standard_metrics(recommendations, ground_truth)

        # 教育专用指标
        educational_metrics = {
            'learning_path_consistency': self.compute_path_consistency(recommendations),
            'prerequisite_compliance': self.compute_prerequisite_compliance(recommendations),
            'concept_coverage': self.compute_concept_coverage(recommendations),
            'difficulty_progression': self.compute_difficulty_progression(recommendations),
            'knowledge_coherence': self.compute_knowledge_coherence(recommendations)
        }

        return {**standard_metrics, **educational_metrics}

    def compute_path_consistency(self, recommendations):
        """计算学习路径一致性"""
        consistency_scores = []

        for user_id, rec_videos in recommendations.items():
            if len(rec_videos) < 2:
                continue

            # 检查推荐视频是否符合学习路径
            path_score = 0
            for i in range(len(rec_videos) - 1):
                current_video = rec_videos[i]
                next_video = rec_videos[i + 1]

                # 检查是否存在合理的学习路径
                if self.has_learning_path(current_video, next_video):
                    path_score += 1

            consistency = path_score / (len(rec_videos) - 1) if len(rec_videos) > 1 else 0
            consistency_scores.append(consistency)

        return np.mean(consistency_scores) if consistency_scores else 0

    def compute_prerequisite_compliance(self, recommendations):
        """计算先修关系遵循度"""
        compliance_scores = []

        for user_id, rec_videos in recommendations.items():
            violations = 0
            total_pairs = 0

            for i in range(len(rec_videos)):
                for j in range(i + 1, len(rec_videos)):
                    video1, video2 = rec_videos[i], rec_videos[j]

                    # 检查是否违反先修关系（后面的视频是前面视频的先修）
                    if self.violates_prerequisite(video1, video2):
                        violations += 1
                    total_pairs += 1

            compliance = 1 - (violations / total_pairs) if total_pairs > 0 else 1
            compliance_scores.append(compliance)

        return np.mean(compliance_scores) if compliance_scores else 0

    def compute_concept_coverage(self, recommendations):
        """计算概念覆盖度"""
        coverage_scores = []

        for user_id, rec_videos in recommendations.items():
            # 获取推荐视频涉及的所有概念
            recommended_concepts = set()
            for video_id in rec_videos:
                video_concepts = self.kg.get_neighbors(video_id, 'video-concept', 'Concept')
                recommended_concepts.update(video_concepts)

            # 获取用户历史学习的概念
            user_history = self.get_user_history(user_id)
            historical_concepts = set()
            for video_id in user_history:
                video_concepts = self.kg.get_neighbors(video_id, 'video-concept', 'Concept')
                historical_concepts.update(video_concepts)

            # 计算概念扩展度
            if len(historical_concepts) > 0:
                new_concepts = recommended_concepts - historical_concepts
                coverage = len(new_concepts) / len(recommended_concepts) if recommended_concepts else 0
            else:
                coverage = 1.0  # 新用户给满分

            coverage_scores.append(coverage)

        return np.mean(coverage_scores) if coverage_scores else 0
```

## 10. 实验设计与预期结果

### 10.1 对比实验设置

**基线方法**：
1. **原始CoLaKG**: 基于课程的物品中心子图
2. **LightGCN**: 纯协同过滤方法
3. **NGCF**: 图神经网络推荐
4. **传统CF**: 基于用户-视频交互的协同过滤

**改进方法**：
1. **CoLaKG-MetaPath**: 基于元路径的视频推荐
2. **CoLaKG-Sequence**: 加入序列感知的学习路径推荐

### 10.2 数据集划分

```python
# 数据集统计
DATASET_STATS = {
    'total_users': 100000,
    'total_videos': 39000,
    'total_interactions': 500000,
    'sparsity': 0.013,  # 相比课程推荐稀疏性大幅降低

    'train_ratio': 0.8,
    'test_ratio': 0.2,
    'cold_start_users': 0.1  # 10%冷启动用户测试
}
```

### 10.3 预期实验结果

**标准推荐指标提升**：
- Recall@10: 15-20%提升（相比原始CoLaKG）
- NDCG@10: 10-15%提升
- 冷启动性能: 25%以上提升

**教育专用指标**：
- 学习路径一致性: >0.8
- 先修关系遵循度: >0.85
- 概念覆盖度: >0.7

**计算效率**：
- 训练时间: 与原始CoLaKG相当
- 推理速度: 略有提升（视频数量多但计算并行度高）
