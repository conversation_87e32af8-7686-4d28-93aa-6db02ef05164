# 多重意图视频推荐系统

基于CoLaKG改进的多重意图视频推荐系统，专门针对MOOCCube数据集进行优化。

## 核心创新

1. **元路径子图提取**: 替代原始的以物品为中心的子图提取，使用6种元路径：
   - V-C: 视频-概念关系
   - V-Course: 视频-课程关系  
   - V-Teacher: 视频-教师关系
   - V-Prerequisite: 视频-先修关系
   - V-U-V: 视频-用户-视频关系
   - V-C-V: 视频-概念-视频关系

2. **多重意图理解**: 5维意图分析
   - 知识获取 (Knowledge Acquisition)
   - 技能提升 (Skill Improvement)  
   - 考试准备 (Exam Preparation)
   - 兴趣探索 (Interest Exploration)
   - 职业发展 (Career Development)

3. **对比学习融合**: InfoNCE损失融合协同过滤嵌入和意图语义嵌入

## 文件结构

```
rec_code/
├── mooccube_dataloader.py      # MOOCCube数据加载器
├── metapath_extractor.py       # 元路径子图提取器
├── multi_intent_llm.py         # 多重意图LLM处理器
├── multi_intent_model.py       # 多重意图推荐模型
├── train_multi_intent.py       # 训练脚本
├── train_mooccube.sh          # 训练启动脚本
├── quick_test.py              # 快速测试脚本
└── README_MULTI_INTENT.md     # 本文件
```

## 环境要求

基于原CoLaKG环境，额外需要：
```bash
pip install sentence-transformers
```

## 数据准备

确保MOOCCube数据集结构如下：
```
data/MOOCCube/
├── entities/
│   ├── user.json
│   ├── video.json
│   ├── concept.json
│   ├── course.json
│   └── teacher.json
├── relations/
│   ├── user-video.json
│   ├── video-concept.json
│   ├── course-video.json
│   ├── video-teacher.json
│   └── prerequisite.json
└── additional_information/
    └── user_video_act.json
```

## 快速开始

### 1. 快速测试
```bash
cd rec_code
python quick_test.py
```

### 2. 完整训练
```bash
cd rec_code
bash train_mooccube.sh
```

### 3. 自定义训练
```bash
cd rec_code
python train_multi_intent.py \
    --dataset mooccube \
    --path ../data/MOOCCube \
    --min_interactions 10 \
    --epochs 1000 \
    --batch_size 2048 \
    --lr 0.001 \
    --contrastive_weight 0.1 \
    --gpu 0
```

## 主要参数

### 数据参数
- `--path`: MOOCCube数据集路径
- `--min_interactions`: 最小交互次数过滤阈值

### 模型参数
- `--latent_dim_rec`: 协同过滤嵌入维度 (默认64)
- `--intent_dim`: 意图嵌入维度 (默认384)
- `--hidden_dim`: 隐藏层维度 (默认512)
- `--final_dim`: 最终嵌入维度 (默认64)
- `--lightGCN_n_layers`: LightGCN层数 (默认3)

### 训练参数
- `--lr`: 学习率 (默认0.001)
- `--batch_size`: 批次大小 (默认2048)
- `--epochs`: 训练轮数 (默认1000)
- `--early_stop`: 早停轮数 (默认50)

### 损失权重
- `--bpr_weight`: BPR损失权重 (默认1.0)
- `--contrastive_weight`: 对比损失权重 (默认0.1)
- `--temperature`: 对比学习温度参数 (默认0.1)

## 输出文件

训练完成后会生成：
- `models/multi_intent_mooccube_v1.pth`: 训练好的模型
- `multi_intent_training_info.json`: 训练信息和结果
- `cache/`: 缓存的数据和嵌入

## 性能优化

1. **缓存机制**: 自动缓存处理过的数据、子图和嵌入
2. **批处理**: LLM处理和嵌入生成使用批处理
3. **GPU加速**: 支持CUDA加速训练
4. **内存优化**: 大规模数据的分批处理

## 故障排除

### 常见问题

1. **数据加载失败**
   - 检查MOOCCube数据集路径和文件结构
   - 确保JSON文件格式正确

2. **内存不足**
   - 减小batch_size
   - 增加min_interactions过滤更多数据
   - 使用更小的嵌入维度

3. **LLM处理慢**
   - 使用`--skip_llm`跳过LLM处理，使用缓存
   - 减小`--llm_batch_size`

4. **训练不收敛**
   - 调整学习率和权重衰减
   - 调整对比损失权重
   - 检查数据质量

### 调试模式

使用快速测试验证各组件：
```bash
python quick_test.py
```

## 扩展功能

1. **可解释性**: 可以基于意图嵌入添加推荐解释
2. **多任务学习**: 可以扩展到课程推荐、学习路径推荐
3. **实时推荐**: 可以部署为在线推荐服务

## 引用

如果使用本代码，请引用原始CoLaKG论文和相关工作。





# 1. 首先测试环境
cd /Desktop/CoLaKG-main/rec_code
python quick_linux_test.py

# 2. 如果环境测试通过，运行简化训练
chmod +x train_simple.sh
bash train_simple.sh
