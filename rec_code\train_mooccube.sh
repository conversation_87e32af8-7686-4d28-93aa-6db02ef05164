#!/bin/bash

# 多重意图视频推荐系统训练脚本
# 使用方法: chmod +x train_mooccube.sh && ./train_mooccube.sh
# 或者: bash train_mooccube.sh

echo "开始训练多重意图视频推荐系统..."

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export TOKENIZERS_PARALLELISM=false

# 创建必要的目录
mkdir -p cache
mkdir -p logs
mkdir -p models

# 基础训练配置
python train_multi_intent.py \
    --dataset mooccube \
    --path ../data/MOOCCube \
    --min_interactions 10 \
    --latent_dim_rec 64 \
    --intent_dim 384 \
    --hidden_dim 512 \
    --final_dim 64 \
    --lightGCN_n_layers 3 \
    --lr 0.001 \
    --decay 1e-4 \
    --batch_size 2048 \
    --epochs 1000 \
    --early_stop 50 \
    --bpr_weight 1.0 \
    --contrastive_weight 0.1 \
    --temperature 0.1 \
    --topks 20 50 \
    --seed 2020 \
    --gpu 0 \
    --comment "multi_intent_video_rec_v1" \
    --save_model "models/multi_intent_mooccube_v1.pth" \
    --llm_batch_size 10

echo "训练完成！"

# 可选：运行测试
echo "开始测试模型..."
python train_multi_intent.py \
    --test_only \
    --load_model "models/multi_intent_mooccube_v1.pth" \
    --path ../data/MOOCCube \
    --min_interactions 10 \
    --skip_llm \
    --comment "test_multi_intent_v1"

echo "测试完成！"
