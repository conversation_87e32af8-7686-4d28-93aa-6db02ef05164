
import argparse


def parse_args():  # 用于从命令行接收并处理实验配置
    # 通过 argparse.ArgumentParser 定义各种超参数和选项，如批大小、嵌入维度、学习率等
    parser = argparse.ArgumentParser(description="Go Model")
    # BPR 损失的批大小
    parser.add_argument('--bpr_batch', type=int,default=2048,
                        help="the batch size for bpr loss training procedure")
    # 嵌入维度
    parser.add_argument('--recdim', type=int,default=64,
                        help="the embedding size")
    # LightGCN 层数
    parser.add_argument('--layer', type=int,default=3,
                        help="the layer num of lightGCN")
    # 语义邻居数
    parser.add_argument('--neighbor_k', type=int,default=10,
                        help="the num of neighbors")
    # 学习率
    parser.add_argument('--lr', type=float,default=0.001,
                        help="the learning rate")
    # L2 正则化权重
    parser.add_argument('--decay', type=float,default=1e-4,
                        help="the weight decay for l2 normalizaton")
    # 是否使用边 Dropout
    parser.add_argument('--use_drop_edge', type=int,default=1,
                        help="using the drop_edge or not for lightgcn")
    # Dropout 保留概率
    parser.add_argument('--keepprob', type=float,default=0.7,
                        help="the batch size for bpr loss training procedure")
    # 项目、用户、邻居的 Dropout 率
    parser.add_argument('--dropout_i', type=float,default=0.6,
                        help="the dropout for item semantic embeddings")
    parser.add_argument('--dropout_u', type=float,default=0.6,
                        help="the dropout for user semantic embeddings")
    parser.add_argument('--dropout_n', type=float,default=0.6,
                        help="the dropout for neighbor embeddings")
    # 邻接矩阵分割折数
    parser.add_argument('--a_fold', type=int,default=100,
                        help="the fold num used to split large adj matrix")
    # 测试批大小
    parser.add_argument('--testbatch', type=int,default=100,
                        help="the batch size of users for testing")
    # 数据集
    parser.add_argument('--dataset', type=str,default='ml-1m',
                        help="available datasets")
    # 模型权重保存路径
    parser.add_argument('--path', type=str,default="./checkpoints",
                        help="path to save weights")
    # 语义嵌入文件路径
    parser.add_argument('--item_semantic_emb_file', type=str,default=" ",
                        help="the path of item_semantic_emb_file")
    parser.add_argument('--user_semantic_emb_file', type=str,default=" ",
                        help="the path of user_semantic_emb_file")
    # Top-K 列表
    parser.add_argument('--topks', nargs='?',default="[10,20]",
                        help="@k test list")
    # 是否启用 TensorBoard
    parser.add_argument('--tensorboard', type=int,default=1,
                        help="enable tensorboard")
    # 实验注释
    parser.add_argument('--comment', type=str,default="lgn")
    # 是否加载预训练模型
    parser.add_argument('--load', type=int,default=0)
    # 训练轮数
    parser.add_argument('--epochs', type=int,default=1000)
    # 测试时是否使用多核
    parser.add_argument('--multicore', type=int, default=0, help='whether we use multiprocessing or not in test')
    # 是否使用预训练权重
    parser.add_argument('--pretrain', type=int, default=0, help='whether we use pretrained weight or not')
    # 随机种子
    parser.add_argument('--seed', type=int, default=2020, help='random seed')
    # 模型类型
    parser.add_argument('--model', type=str, default='colakg', help='rec-model, support [mf, lgn, colakg]')
    return parser.parse_args()
