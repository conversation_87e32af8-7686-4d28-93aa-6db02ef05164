#!/usr/bin/env python3
"""
测试多重意图视频推荐系统的各个组件
"""

import os
import sys
import torch
import numpy as np
import json

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_loading():
    """测试数据加载"""
    print("=== 测试数据加载 ===")
    
    try:
        from mooccube_dataloader import MOOCCubeDataset
        
        # 检查数据目录是否存在
        data_path = "../data/MOOCCube"
        if not os.path.exists(data_path):
            print(f"警告: 数据目录 {data_path} 不存在")
            print("请确保MOOCCube数据集已正确放置")
            return False
        
        print("正在加载MOOCCube数据集...")
        dataset = MOOCCubeDataset(
            path=data_path,
            min_interactions=5,  # 降低阈值以便测试
            cache_dir="test_cache"
        )
        
        print(f"数据集加载成功:")
        print(f"  用户数: {dataset.n_users}")
        print(f"  视频数: {dataset.m_items}")
        print(f"  训练交互数: {dataset.trainDataSize}")
        print(f"  测试用户数: {len(dataset.testDict)}")
        
        return True
        
    except Exception as e:
        print(f"数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_metapath_extraction():
    """测试元路径提取"""
    print("\n=== 测试元路径提取 ===")
    
    try:
        from mooccube_dataloader import MOOCCubeDataset
        from metapath_extractor import MetaPathSubgraphExtractor
        
        # 创建小规模数据集用于测试
        dataset = MOOCCubeDataset(
            path="../data/MOOCCube",
            min_interactions=5,
            cache_dir="test_cache"
        )
        
        extractor = MetaPathSubgraphExtractor(dataset, cache_dir="test_cache")
        
        # 测试视频子图提取
        video_ids = list(dataset.video_id_map.keys())[:5]  # 只测试前5个视频
        print(f"测试 {len(video_ids)} 个视频的子图提取...")
        
        video_subgraphs = extractor.batch_extract_video_subgraphs(video_ids, max_neighbors=10)
        
        for video_id, subgraph in video_subgraphs.items():
            text = extractor.convert_subgraph_to_text(subgraph, 'video')
            print(f"视频 {video_id}: {text[:100]}...")
        
        # 测试用户子图提取
        user_indices = list(range(min(5, dataset.n_users)))  # 只测试前5个用户
        print(f"测试 {len(user_indices)} 个用户的子图提取...")
        
        user_subgraphs = extractor.batch_extract_user_subgraphs(user_indices, max_videos=20)
        
        for user_idx, subgraph in user_subgraphs.items():
            text = extractor.convert_subgraph_to_text(subgraph, 'user')
            print(f"用户 {user_idx}: {text[:100]}...")
        
        print("元路径提取测试成功!")
        return True
        
    except Exception as e:
        print(f"元路径提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_processing():
    """测试LLM处理（使用模拟）"""
    print("\n=== 测试LLM处理 ===")
    
    try:
        from multi_intent_llm import MultiIntentLLMProcessor, IntentEmbeddingProcessor
        
        # 创建测试数据
        video_texts = {
            "video_1": "视频名称：机器学习基础；相关概念：监督学习, 无监督学习；所属课程：人工智能导论",
            "video_2": "视频名称：深度学习入门；相关概念：神经网络, 反向传播；所属课程：深度学习"
        }
        
        user_texts = {
            0: "观看视频数量：15；主要学习概念：机器学习(5次), 深度学习(3次)；主要学习课程：人工智能导论(8个视频)",
            1: "观看视频数量：23；主要学习概念：数据结构(8次), 算法(6次)；主要学习课程：计算机科学基础(12个视频)"
        }
        
        # 测试LLM处理
        llm_processor = MultiIntentLLMProcessor(cache_dir="test_cache", batch_size=2)
        
        print("处理视频意图...")
        video_intent_texts = llm_processor.process_video_intents(video_texts)
        
        print("处理用户意图...")
        user_intent_texts = llm_processor.process_user_intents(user_texts)
        
        # 测试嵌入处理
        embedding_processor = IntentEmbeddingProcessor(cache_dir="test_cache")
        
        print("生成视频嵌入...")
        video_embeddings = embedding_processor.process_video_intent_embeddings(video_intent_texts)
        
        print("生成用户嵌入...")
        user_embeddings = embedding_processor.process_user_intent_embeddings(user_intent_texts)
        
        print(f"LLM处理测试成功!")
        print(f"  视频意图: {len(video_intent_texts)} 个")
        print(f"  用户意图: {len(user_intent_texts)} 个")
        print(f"  视频嵌入: {len(video_embeddings)} 个, 维度: {len(list(video_embeddings.values())[0])}")
        print(f"  用户嵌入: {len(user_embeddings)} 个, 维度: {len(list(user_embeddings.values())[0])}")
        
        return True
        
    except Exception as e:
        print(f"LLM处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n=== 测试模型创建 ===")
    
    try:
        from mooccube_dataloader import MOOCCubeDataset
        from multi_intent_model import MultiIntentVideoRecommender
        import world
        
        # 创建小规模数据集
        dataset = MOOCCubeDataset(
            path="../data/MOOCCube",
            min_interactions=5,
            cache_dir="test_cache"
        )
        
        # 创建模拟嵌入
        video_intent_embeddings = {}
        user_intent_embeddings = {}
        
        for video_idx in range(min(10, dataset.m_items)):
            video_id = dataset.idx_to_video.get(video_idx, f"video_{video_idx}")
            video_intent_embeddings[video_id] = np.random.randn(384).tolist()
        
        for user_idx in range(min(10, dataset.n_users)):
            user_intent_embeddings[user_idx] = np.random.randn(384).tolist()
        
        # 配置
        config = {
            'latent_dim_rec': 64,
            'intent_dim': 384,
            'hidden_dim': 512,
            'final_dim': 64,
            'lightGCN_n_layers': 3,
            'dropout': 0,
            'A_split': False,
            'A_n_fold': 100,
            'temperature': 0.1
        }
        
        # 设置world配置
        world.config = config
        world.device = torch.device('cpu')  # 使用CPU进行测试
        
        # 创建模型
        model = MultiIntentVideoRecommender(
            config=config,
            dataset=dataset,
            video_intent_embeddings=video_intent_embeddings,
            user_intent_embeddings=user_intent_embeddings
        )
        
        print(f"模型创建成功!")
        print(f"  参数数量: {sum(p.numel() for p in model.parameters())}")
        
        # 测试前向传播
        batch_size = 4
        users = torch.randint(0, dataset.n_users, (batch_size,))
        pos_items = torch.randint(0, dataset.m_items, (batch_size,))
        neg_items = torch.randint(0, dataset.m_items, (batch_size,))
        
        print("测试前向传播...")
        users_emb, pos_emb, neg_emb, contrastive_loss = model(users, pos_items, neg_items)
        
        print(f"  用户嵌入形状: {users_emb.shape}")
        print(f"  正样本嵌入形状: {pos_emb.shape}")
        print(f"  负样本嵌入形状: {neg_emb.shape}")
        print(f"  对比损失: {contrastive_loss.item():.4f}")
        
        # 测试预测
        print("测试预测...")
        scores = model.predict(users, pos_items)
        print(f"  预测评分形状: {scores.shape}")
        print(f"  预测评分范围: [{scores.min().item():.4f}, {scores.max().item():.4f}]")
        
        return True
        
    except Exception as e:
        print(f"模型创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试多重意图视频推荐系统实现")
    print("=" * 50)
    
    # 创建测试缓存目录
    os.makedirs("test_cache", exist_ok=True)
    
    tests = [
        ("数据加载", test_data_loading),
        ("元路径提取", test_metapath_extraction),
        ("LLM处理", test_llm_processing),
        ("模型创建", test_model_creation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"{test_name} 测试出现异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    for test_name, success in results.items():
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    passed = sum(results.values())
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 实现基本正确。")
    else:
        print("⚠️  部分测试失败，请检查相关组件。")

if __name__ == "__main__":
    main()
