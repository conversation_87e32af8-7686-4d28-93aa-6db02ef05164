#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仅测试数据加载，不进行训练
"""

import os
import sys
import time

def main():
    """主函数"""
    print("=" * 60)
    print("MOOCCube数据加载测试")
    print("=" * 60)
    
    try:
        # 导入数据加载器
        from mooccube_dataloader import MOOCCubeDataset
        
        print("开始加载MOOCCube数据集...")
        start_time = time.time()
        
        # 创建数据集实例
        dataset = MOOCCubeDataset(
            path="../data/MOOCCube",
            min_interactions=5,  # 使用较低的阈值
            cache_dir="test_cache"
        )
        
        load_time = time.time() - start_time
        
        print(f"\n✅ 数据加载成功！")
        print(f"⏱️  加载时间: {load_time:.2f}秒")
        print(f"👥 用户数: {dataset.n_users:,}")
        print(f"🎥 视频数: {dataset.m_items:,}")
        print(f"📚 训练交互数: {dataset.trainDataSize:,}")
        print(f"🧪 测试交互数: {dataset.testDataSize:,}")
        
        # 测试基本功能
        print(f"\n🔍 测试基本功能...")
        
        # 测试稀疏图
        try:
            sparse_graph = dataset.getSparseGraph()
            print(f"✅ 稀疏图构建成功: {sparse_graph.shape}")
        except Exception as e:
            print(f"❌ 稀疏图构建失败: {e}")
        
        # 测试采样
        try:
            users, pos_items, neg_items = dataset.sample()
            print(f"✅ 批次采样成功: {len(users)} 个样本")
        except Exception as e:
            print(f"❌ 批次采样失败: {e}")
        
        print(f"\n🎉 所有测试通过！数据加载器工作正常")
        return True
        
    except Exception as e:
        print(f"\n❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理缓存
        import shutil
        if os.path.exists("test_cache"):
            try:
                shutil.rmtree("test_cache")
                print("🧹 清理缓存完成")
            except:
                pass

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✨ 数据加载测试完成，可以开始训练了！")
        sys.exit(0)
    else:
        print("\n💥 数据加载测试失败，请检查数据格式")
        sys.exit(1)
