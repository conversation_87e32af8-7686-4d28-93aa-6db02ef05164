import os
from os.path import join
import torch
from enum import Enum
from parse import parse_args
import multiprocessing

os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'
args = parse_args()

ROOT_PATH = os.path.dirname(os.path.dirname(__file__))  # 项目根目录，基于当前文件位置向上两级
CODE_PATH = join(ROOT_PATH, 'code')  # 代码目录
DATA_PATH = join(ROOT_PATH, 'data')  # 数据目录
BOARD_PATH = join(CODE_PATH, 'runs')  # TensorBoard 日志目录
FILE_PATH = join(CODE_PATH, 'checkpoints')  # 模型检查点保存目录
import sys
# 将 sources 目录添加到 Python 模块搜索路径，确保 rec_code/sources 中的模块（如 dataloader.py）可被导入
sys.path.append(join(CODE_PATH, 'sources'))

# 如果 checkpoints 目录不存在，创建它。exist_ok=True 防止重复创建时抛出异常
# 保存模型权重或训练过程中的检查点文件
if not os.path.exists(FILE_PATH):
    os.makedirs(FILE_PATH, exist_ok=True)


config = {}  # 空字典，用于存储所有超参数和设置
all_dataset = ['lastfm', 'ml-1m', 'mind', 'fund']  # 支持的数据集列表
all_models  = ['mf', 'lgn', 'colakg']  # 支持的模型类型

config['bpr_batch_size'] = args.bpr_batch  # BPR 损失的批大小
config['latent_dim_rec'] = args.recdim  # 推荐模型的嵌入维度
config['lightGCN_n_layers']= args.layer  # LightGCN 的层数
config['use_drop_edge'] = args.use_drop_edge  # 是否使用边 dropout
config['keep_prob']  = args.keepprob  # 保留概率，用于 dropout
config['A_n_fold'] = args.a_fold  # 邻接矩阵折叠次数
config['test_u_batch_size'] = args.testbatch  # 测试批大小
config['multicore'] = args.multicore  # 是否使用多核处理
config['lr'] = args.lr  # 学习率
config['decay'] = args.decay  # 权重衰减
config['pretrain'] = args.pretrain  # 是否使用预训练模型
config['A_split'] = False  # 是否分割邻接矩阵
config['bigdata'] = False  # 是否处理大数据集
config['neighbor_k'] = args.neighbor_k  # 检索的语义邻居数
config['dropout_i'] = args.dropout_i  # 项目嵌入的 dropout 概率
config['dropout_u'] = args.dropout_u  # 用户嵌入的 dropout 概率
config['dropout_n'] = args.dropout_n  # 邻居嵌入的 dropout 概率

# 检测是否可用 GPU，若可用则使用 CUDA，否则使用 CPU
GPU = torch.cuda.is_available()
device = torch.device('cuda' if GPU else "cpu")
CORES = multiprocessing.cpu_count() // 2
seed = args.seed

# 验证 dataset 和 model_name 是否在支持列表中，否则抛出异常
dataset = args.dataset
model_name = args.model
if dataset not in all_dataset:
    raise NotImplementedError(f"Haven't supported {dataset} yet!, try {all_dataset}")
if model_name not in all_models:
    raise NotImplementedError(f"Haven't supported {model_name} yet!, try {all_models}")

# 指定项目和用户语义嵌入文件的路径（例如 lastfm_embeddings_simcse_kg.pt），由命令行传入
item_semantic_emb_file = args.item_semantic_emb_file
user_semantic_emb_file = args.user_semantic_emb_file

# TRAIN_epochs = args.epochs
TRAIN_epochs = 2000  # 训练轮数
LOAD = args.load  # 是否加载已有模型检查点
PATH = args.path  # 模型或数据的保存/加载路径
topks = eval(args.topks)  # 评估的 Top-K 值
tensorboard = args.tensorboard  # 是否启用 TensorBoard 记录训练过程
comment = args.comment  # 实验注释，用于区分不同运行
# let pandas shut up
from warnings import simplefilter
simplefilter(action="ignore", category=FutureWarning)



def cprint(words : str):
    print(f"\033[0;30;43m{words}\033[0m")

