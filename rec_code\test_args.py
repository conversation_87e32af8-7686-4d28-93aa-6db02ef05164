#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参数解析
"""

import argparse
import sys

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='多重意图视频推荐系统训练')

    # 数据配置
    parser.add_argument('--dataset', type=str, default='mooccube', help='数据集名称')
    parser.add_argument('--path', type=str, default='../data/MOOCCube', help='数据集路径')
    parser.add_argument('--min_interactions', type=int, default=10, help='最小交互次数')

    # 模型配置
    parser.add_argument('--latent_dim_rec', type=int, default=64, help='协同过滤嵌入维度')
    parser.add_argument('--intent_dim', type=int, default=384, help='意图嵌入维度')
    parser.add_argument('--hidden_dim', type=int, default=512, help='隐藏层维度')
    parser.add_argument('--final_dim', type=int, default=64, help='最终嵌入维度')
    parser.add_argument('--lightGCN_n_layers', type=int, default=3, help='LightGCN层数')
    parser.add_argument('--dropout', type=float, default=0, help='Dropout率')

    # 训练配置
    parser.add_argument('--lr', type=float, default=0.001, help='学习率')
    parser.add_argument('--decay', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--batch_size', type=int, default=2048, help='批次大小')
    parser.add_argument('--epochs', type=int, default=1000, help='训练轮数')
    parser.add_argument('--early_stop', type=int, default=50, help='早停轮数')
    parser.add_argument('--test_u_batch_size', type=int, default=100, help='测试批次大小')

    # 损失权重
    parser.add_argument('--bpr_weight', type=float, default=1.0, help='BPR损失权重')
    parser.add_argument('--contrastive_weight', type=float, default=0.1, help='对比损失权重')
    parser.add_argument('--temperature', type=float, default=0.1, help='对比学习温度参数')

    # 其他配置
    parser.add_argument('--topks', type=int, nargs='+', default=[20, 50], help='评估的top-k值')
    parser.add_argument('--seed', type=int, default=2020, help='随机种子')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备号')
    parser.add_argument('--comment', type=str, default='multi_intent_video_rec', help='实验注释')

    # 模式配置
    parser.add_argument('--test_only', action='store_true', help='仅测试模式')
    parser.add_argument('--load_model', type=str, default='', help='加载模型路径')
    parser.add_argument('--save_model', type=str, default='best_multi_intent_model.pth', help='保存模型路径')

    # LLM配置
    parser.add_argument('--skip_llm', action='store_true', help='跳过LLM处理，使用缓存')
    parser.add_argument('--llm_batch_size', type=int, default=10, help='LLM处理批次大小')

    args = parser.parse_args()

    # 转换为字典格式
    config = vars(args)

    # 添加固定配置
    config.update({
        'A_split': False,
        'A_n_fold': 100,
        'tensorboard': True,
        'load': 0,
        'multicore': 0,
        'pretrain': 0,
        'model': 'multi_intent'
    })

    return config

def main():
    """主函数"""
    print("测试参数解析...")
    print(f"命令行参数: {sys.argv}")
    
    try:
        config = parse_args()
        print("✅ 参数解析成功!")
        print("配置参数:")
        for key, value in config.items():
            print(f"  {key}: {value}")
    except Exception as e:
        print(f"❌ 参数解析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
