#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查关系文件格式
"""

import os
import json

def load_json_file(file_path):
    """加载JSON文件，支持多种格式"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return []
    
    print(f"检查文件: {file_path}")
    
    # 尝试标准JSON格式
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"  ✓ 标准JSON格式，{len(data) if isinstance(data, list) else 1} 个对象")
        if isinstance(data, list) and len(data) > 0:
            print(f"  第一个对象: {data[0]}")
        elif not isinstance(data, list):
            print(f"  对象内容: {data}")
        return data if isinstance(data, list) else [data]
    except json.JSONDecodeError as e:
        print(f"  标准JSON失败: {e}")
    
    # 尝试JSONL格式（每行一个JSON对象）
    try:
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f):
                line = line.strip()
                if line:
                    try:
                        obj = json.loads(line)
                        data.append(obj)
                    except json.JSONDecodeError as e:
                        print(f"  第{line_num+1}行解析失败: {e}")
                        continue
                if line_num >= 10:  # 只读前10行测试
                    break
        
        if data:
            print(f"  ✓ JSONL格式，检查了 {len(data)} 个对象")
            print(f"  第一个对象: {data[0]}")
            return data
    except Exception as e:
        print(f"  JSONL失败: {e}")
    
    # 尝试连续JSON对象格式
    try:
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        decoder = json.JSONDecoder()
        idx = 0
        while idx < len(content) and len(data) < 10:  # 限制数量
            content_part = content[idx:].lstrip()
            if not content_part:
                break
            try:
                obj, end_idx = decoder.raw_decode(content_part)
                data.append(obj)
                idx += end_idx
            except json.JSONDecodeError:
                break
        
        if data:
            print(f"  ✓ 连续JSON格式，检查了 {len(data)} 个对象")
            print(f"  第一个对象: {data[0]}")
            return data
    except Exception as e:
        print(f"  连续JSON失败: {e}")
    
    print(f"  ✗ 无法解析")
    return []

def main():
    """主函数"""
    print("检查MOOCCube关系文件")
    print("=" * 50)
    
    # 检查关键关系文件
    relation_files = [
        "../data/MOOCCube/relations/user-video.json",
        "../data/MOOCCube/relations/video-concept.json", 
        "../data/MOOCCube/relations/course-video.json",
        "../data/MOOCCube/additional_information/user_video_act.json"
    ]
    
    for file_path in relation_files:
        print(f"\n{'='*60}")
        data = load_json_file(file_path)
        
        if data:
            print(f"  数据量: {len(data)}")
            if len(data) > 0:
                # 分析数据结构
                first_obj = data[0]
                print(f"  字段: {list(first_obj.keys()) if isinstance(first_obj, dict) else 'Not a dict'}")
                
                # 检查是否有用户-视频交互数据
                if 'user' in str(first_obj).lower() and 'video' in str(first_obj).lower():
                    print(f"  ✓ 包含用户-视频交互数据")
                else:
                    print(f"  - 不是用户-视频交互数据")
        else:
            print(f"  ✗ 无数据")

if __name__ == "__main__":
    main()
