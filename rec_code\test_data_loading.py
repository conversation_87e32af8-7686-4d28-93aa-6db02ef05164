#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试数据加载
"""

import os
import sys
import json

def test_json_loading():
    """测试JSON文件加载"""
    print("测试JSON文件加载...")
    
    def load_json_file(file_path):
        """加载JSON文件，支持多种格式"""
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return []
        
        print(f"尝试加载: {file_path}")
        
        # 尝试标准JSON格式
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"  ✓ 标准JSON格式，{len(data) if isinstance(data, list) else 1} 个对象")
            return data if isinstance(data, list) else [data]
        except json.JSONDecodeError as e:
            print(f"  标准JSON失败: {e}")
        
        # 尝试JSONL格式（每行一个JSON对象）
        try:
            data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    line = line.strip()
                    if line:
                        try:
                            obj = json.loads(line)
                            data.append(obj)
                        except json.JSONDecodeError as e:
                            print(f"  第{line_num+1}行解析失败: {e}")
                            continue
                    if line_num >= 100:  # 只读前100行测试
                        break
            
            if data:
                print(f"  ✓ JSONL格式，{len(data)} 个对象")
                return data
        except Exception as e:
            print(f"  JSONL失败: {e}")
        
        # 尝试连续JSON对象格式
        try:
            data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            decoder = json.JSONDecoder()
            idx = 0
            while idx < len(content) and len(data) < 100:  # 限制数量
                content_part = content[idx:].lstrip()
                if not content_part:
                    break
                try:
                    obj, end_idx = decoder.raw_decode(content_part)
                    data.append(obj)
                    idx += end_idx
                except json.JSONDecodeError:
                    break
            
            if data:
                print(f"  ✓ 连续JSON格式，{len(data)} 个对象")
                return data
        except Exception as e:
            print(f"  连续JSON失败: {e}")
        
        print(f"  ✗ 无法解析")
        return []
    
    # 测试关键文件
    test_files = [
        "../data/MOOCCube/entities/user.json",
        "../data/MOOCCube/entities/video.json",
        "../data/MOOCCube/entities/concept.json",
        "../data/MOOCCube/entities/course.json",
        "../data/MOOCCube/relations/user-video.json",
        "../data/MOOCCube/relations/video-concept.json",
        "../data/MOOCCube/relations/course-video.json"
    ]
    
    results = {}
    for file_path in test_files:
        print(f"\n{'='*50}")
        data = load_json_file(file_path)
        results[file_path] = len(data)
        
        if data and len(data) > 0:
            print(f"  第一个对象示例: {str(data[0])[:200]}...")
    
    print(f"\n{'='*50}")
    print("加载结果总结:")
    for file_path, count in results.items():
        filename = os.path.basename(file_path)
        print(f"  {filename}: {count} 个对象")
    
    return results

def test_mooccube_dataset():
    """测试MOOCCube数据集加载"""
    print("\n测试MOOCCube数据集加载...")
    
    try:
        from mooccube_dataloader import MOOCCubeDataset
        
        print("创建数据集实例...")
        dataset = MOOCCubeDataset(
            path="../data/MOOCCube",
            min_interactions=5,  # 降低阈值
            cache_dir="test_cache"
        )
        
        print(f"✓ 数据集加载成功!")
        print(f"  用户数: {dataset.n_users}")
        print(f"  视频数: {dataset.m_items}")
        print(f"  训练交互数: {dataset.trainDataSize}")
        print(f"  测试交互数: {dataset.testDataSize}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据集加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("数据加载测试")
    print("=" * 60)
    
    # 创建测试目录
    os.makedirs("test_cache", exist_ok=True)
    
    # 测试JSON文件加载
    json_results = test_json_loading()
    
    # 测试数据集加载
    dataset_success = test_mooccube_dataset()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    
    total_objects = sum(json_results.values())
    print(f"JSON文件加载: {total_objects} 个对象")
    
    if dataset_success:
        print("数据集加载: ✓ 成功")
    else:
        print("数据集加载: ✗ 失败")
    
    # 清理
    import shutil
    if os.path.exists("test_cache"):
        shutil.rmtree("test_cache")

if __name__ == "__main__":
    main()
