#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量级测试脚本 - 使用小规模数据测试
"""

import os
import sys
import torch
import numpy as np
import time

def create_mini_dataset():
    """创建小规模测试数据集"""
    print("🔧 创建小规模测试数据集...")
    
    # 模拟小规模数据集
    class MiniDataset:
        def __init__(self):
            self.n_users = 100
            self.m_items = 50
            self.trainDataSize = 1000
            self.testDataSize = 200
            
            # 创建模拟的训练数据
            self.trainUser = np.random.randint(0, self.n_users, self.trainDataSize)
            self.trainItem = np.random.randint(0, self.m_items, self.trainDataSize)
            self.train_data = list(zip(self.trainUser, self.trainItem))
            self.test_data = [(np.random.randint(0, self.n_users), np.random.randint(0, self.m_items)) 
                             for _ in range(self.testDataSize)]
            
            # 创建allPos字典
            self.allPos = {}
            for user in range(self.n_users):
                user_items = [item for u, item in self.train_data if u == user]
                self.allPos[user] = user_items if user_items else [0]
            
            # 创建测试字典
            self._testDict = {}
            for user, item in self.test_data:
                if user not in self._testDict:
                    self._testDict[user] = []
                self._testDict[user].append(item)
            
            # 创建模拟的稀疏图
            self.Graph = torch.sparse_coo_tensor(
                torch.LongTensor([[0, 1], [1, 0]]),
                torch.FloatTensor([1.0, 1.0]),
                (self.n_users + self.m_items, self.n_users + self.m_items)
            )
        
        @property
        def testDict(self):
            return self._testDict
        
        def getSparseGraph(self):
            return self.Graph
        
        def sample(self, batch_size=32):
            """采样训练批次"""
            users = np.random.choice(self.trainUser, size=batch_size, replace=True)
            pos_items = []
            neg_items = []
            
            for user in users:
                user_pos_items = self.allPos[user]
                pos_item = np.random.choice(user_pos_items)
                pos_items.append(pos_item)
                
                while True:
                    neg_item = np.random.randint(0, self.m_items)
                    if neg_item not in user_pos_items:
                        break
                neg_items.append(neg_item)
            
            return np.array(users), np.array(pos_items), np.array(neg_items)
    
    return MiniDataset()

def test_model_with_mini_data():
    """使用小规模数据测试模型"""
    print("=" * 60)
    print("轻量级模型测试")
    print("=" * 60)
    
    # 1. 创建小规模数据集
    dataset = create_mini_dataset()
    print(f"✅ 小规模数据集创建成功")
    print(f"   用户数: {dataset.n_users}")
    print(f"   视频数: {dataset.m_items}")
    print(f"   训练交互: {dataset.trainDataSize}")
    
    # 2. 测试模型创建
    print(f"\n🤖 测试模型创建...")
    try:
        from multi_intent_model import MultiIntentVideoRecommender
        
        config = {
            'latent_dim_rec': 32,  # 减小维度
            'intent_dim': 64,      # 减小维度
            'hidden_dim': 128,     # 减小维度
            'final_dim': 32,       # 减小维度
            'lightGCN_n_layers': 2, # 减少层数
            'dropout': 0.0,
            'A_split': False,
            'pretrain': 0,
            'bpr_weight': 1.0,
            'contrastive_weight': 0.1,
            'temperature': 0.1
        }
        
        # 创建小规模的意图嵌入
        video_intent_embeddings = np.random.randn(dataset.m_items, 64).astype(np.float32)
        user_intent_embeddings = np.random.randn(dataset.n_users, 64).astype(np.float32)
        
        print(f"   创建意图嵌入: 视频 {video_intent_embeddings.shape}, 用户 {user_intent_embeddings.shape}")
        
        model = MultiIntentVideoRecommender(
            config=config,
            dataset=dataset,
            video_intent_embeddings=video_intent_embeddings,
            user_intent_embeddings=user_intent_embeddings
        )
        
        print(f"✅ 模型创建成功")
        print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 3. 测试训练
    print(f"\n🏃 测试训练...")
    try:
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        model.train()
        users, pos_items, neg_items = dataset.sample(batch_size=16)
        
        users = torch.LongTensor(users)
        pos_items = torch.LongTensor(pos_items)
        neg_items = torch.LongTensor(neg_items)
        
        optimizer.zero_grad()
        loss = model.bpr_loss(users, pos_items, neg_items)
        loss.backward()
        optimizer.step()
        
        print(f"✅ 训练测试成功，损失: {loss.item():.4f}")
        
    except Exception as e:
        print(f"❌ 训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 4. 测试多个训练步骤
    print(f"\n🔄 测试多步训练...")
    try:
        for step in range(5):
            users, pos_items, neg_items = dataset.sample(batch_size=16)
            
            users = torch.LongTensor(users)
            pos_items = torch.LongTensor(pos_items)
            neg_items = torch.LongTensor(neg_items)
            
            optimizer.zero_grad()
            loss = model.bpr_loss(users, pos_items, neg_items)
            loss.backward()
            optimizer.step()
            
            print(f"   步骤 {step+1}: 损失 = {loss.item():.4f}")
        
        print(f"✅ 多步训练测试成功")
        
    except Exception as e:
        print(f"❌ 多步训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print(f"\n🎉 所有轻量级测试通过！")
    return True

def main():
    """主函数"""
    success = test_model_with_mini_data()
    
    if success:
        print(f"\n✨ 轻量级测试完成！")
        print(f"💡 模型架构验证成功，可以进行大规模训练")
        print(f"🚀 建议在Linux服务器上运行完整训练")
    else:
        print(f"\n💥 轻量级测试失败")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
