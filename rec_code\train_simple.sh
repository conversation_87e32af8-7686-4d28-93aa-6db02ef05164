#!/bin/bash

# 简化的MOOCCube训练脚本 - Linux版本
echo "开始训练MOOCCube多重意图视频推荐系统..."

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export TOKENIZERS_PARALLELISM=false

# 创建必要的目录
mkdir -p cache_linux
mkdir -p logs
mkdir -p models

# 基础训练 - 使用较小的参数进行快速测试
echo "开始基础训练..."
python train_mooccube_simple.py \
    --path ../data/MOOCCube \
    --min_interactions 10 \
    --epochs 50 \
    --batch_size 1024 \
    --lr 0.001 \
    --gpu 0 \
    --seed 2020 \
    --skip_llm

echo "训练完成！"

# 检查模型文件是否生成
if [ -f "multi_intent_mooccube_linux_10core.pth" ]; then
    echo "✅ 模型文件已生成: multi_intent_mooccube_linux_10core.pth"
    ls -lh multi_intent_mooccube_linux_10core.pth
else
    echo "❌ 模型文件未生成"
fi

echo "训练脚本执行完成！"
