import os
import json
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset
from scipy.sparse import csr_matrix
import scipy.sparse as sp
from collections import defaultdict
import pickle
from time import time
try:
    from dataloader import BasicDataset
except ImportError:
    # 如果导入失败，定义一个简单的基类
    from torch.utils.data import Dataset
    class BasicDataset(Dataset):
        def __init__(self):
            pass

        @property
        def n_users(self):
            raise NotImplementedError

        @property
        def m_items(self):
            raise NotImplementedError

        @property
        def trainDataSize(self):
            raise NotImplementedError

        @property
        def testDict(self):
            raise NotImplementedError

        @property
        def allPos(self):
            raise NotImplementedError

        def getSparseGraph(self):
            raise NotImplementedError
import world
from world import cprint

class MOOCCubeDataset(BasicDataset):
    """
    MOOCCube数据集加载器，支持多重意图视频推荐
    """
    def __init__(self, path="../data/MOOCCube", min_interactions=10, cache_dir="cache"):
        cprint("Loading MOOCCube dataset for video recommendation...")
        
        self.path = path
        self.min_interactions = min_interactions
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
        # 检查是否有缓存
        cache_file = os.path.join(cache_dir, f"mooccube_processed_{min_interactions}core.pkl")
        if os.path.exists(cache_file):
            cprint("Loading from cache...")
            self._load_from_cache(cache_file)
        else:
            cprint("Processing raw data...")
            self._load_and_process_data()
            self._save_to_cache(cache_file)
        
        self._build_sparse_graph()
        cprint(f"Dataset loaded: {self.n_users} users, {self.m_items} videos, {self.trainDataSize} interactions")
    
    def _load_and_process_data(self):
        """加载并处理原始数据"""
        # 1. 加载实体数据
        self._load_entities()
        
        # 2. 加载关系数据
        self._load_relations()
        
        # 3. 应用10核过滤
        self._apply_core_filtering()
        
        # 4. 构建知识图谱
        self._build_knowledge_graph()
        
        # 5. 划分训练测试集
        self._split_train_test()

    def _load_json_file(self, file_path):
        """加载数据文件，支持JSON和制表符分隔格式"""
        if not os.path.exists(file_path):
            return []

        # 首先检查文件是否在relations目录中，这些文件是制表符分隔的
        if "relations" in file_path or "additional_information" in file_path:
            return self._load_tab_separated_file(file_path)

        # 对于entities目录的文件，尝试JSON格式
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data if isinstance(data, list) else [data]
        except json.JSONDecodeError:
            pass

        # 尝试JSONL格式（每行一个JSON对象）
        try:
            data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            obj = json.loads(line)
                            data.append(obj)
                        except json.JSONDecodeError:
                            continue
            if data:
                return data
        except Exception:
            pass

        # 最后尝试制表符分隔格式
        return self._load_tab_separated_file(file_path)

    def _load_tab_separated_file(self, file_path):
        """加载制表符分隔的文件"""
        try:
            data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    line = line.strip()
                    if line:
                        # 按制表符分割
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            data.append(parts)
                        elif len(parts) == 1:
                            # 可能是空格分隔
                            parts = line.split()
                            if len(parts) >= 2:
                                data.append(parts)

            print(f"从 {file_path} 加载了 {len(data)} 条记录")
            if data and len(data) > 0:
                print(f"  样本: {data[0]}")
            return data

        except Exception as e:
            print(f"警告: 无法解析文件 {file_path}: {e}")
            return []

    def _load_entities(self):
        """加载实体数据"""
        entities_path = os.path.join(self.path, "entities")

        print(f"正在从 {entities_path} 加载实体数据...")

        # 检查entities目录是否存在
        if not os.path.exists(entities_path):
            print(f"警告: entities目录不存在: {entities_path}")
            print("尝试查找数据文件...")
            # 列出可用的目录
            if os.path.exists(self.path):
                available_dirs = [d for d in os.listdir(self.path) if os.path.isdir(os.path.join(self.path, d))]
                print(f"可用目录: {available_dirs}")

            # 初始化空的实体信息
            self.user_info = {}
            self.video_info = {}
            self.concept_info = {}
            self.course_info = {}
            self.teacher_info = {}
            return

        # 加载用户
        users = self._load_json_file(os.path.join(entities_path, "user.json"))
        self.user_info = {}
        for user in users:
            if isinstance(user, dict) and 'id' in user:
                self.user_info[user['id']] = user
            elif isinstance(user, (list, tuple)) and len(user) >= 1:
                # 制表符分隔格式，第一个字段是ID
                user_id = user[0].strip()
                self.user_info[user_id] = {'id': user_id}

        # 加载视频
        videos = self._load_json_file(os.path.join(entities_path, "video.json"))
        self.video_info = {}
        for video in videos:
            if isinstance(video, dict) and 'id' in video:
                self.video_info[video['id']] = video
            elif isinstance(video, (list, tuple)) and len(video) >= 1:
                # 制表符分隔格式，第一个字段是ID
                video_id = video[0].strip()
                self.video_info[video_id] = {'id': video_id}

        # 加载概念
        concepts = self._load_json_file(os.path.join(entities_path, "concept.json"))
        self.concept_info = {}
        for concept in concepts:
            if isinstance(concept, dict) and 'id' in concept:
                self.concept_info[concept['id']] = concept
            elif isinstance(concept, (list, tuple)) and len(concept) >= 1:
                # 制表符分隔格式，第一个字段是ID
                concept_id = concept[0].strip()
                self.concept_info[concept_id] = {'id': concept_id}

        # 加载课程
        courses = self._load_json_file(os.path.join(entities_path, "course.json"))
        self.course_info = {}
        for course in courses:
            if isinstance(course, dict) and 'id' in course:
                self.course_info[course['id']] = course
            elif isinstance(course, (list, tuple)) and len(course) >= 1:
                # 制表符分隔格式，第一个字段是ID
                course_id = course[0].strip()
                self.course_info[course_id] = {'id': course_id}

        # 加载教师
        teachers = self._load_json_file(os.path.join(entities_path, "teacher.json"))
        self.teacher_info = {}
        for teacher in teachers:
            if isinstance(teacher, dict) and 'id' in teacher:
                self.teacher_info[teacher['id']] = teacher
            elif isinstance(teacher, (list, tuple)) and len(teacher) >= 1:
                # 制表符分隔格式，第一个字段是ID
                teacher_id = teacher[0].strip()
                self.teacher_info[teacher_id] = {'id': teacher_id}

        print(f"Loaded entities: {len(self.user_info)} users, {len(self.video_info)} videos, "
              f"{len(self.concept_info)} concepts, {len(self.course_info)} courses, {len(self.teacher_info)} teachers")
    
    def _load_relations(self):
        """加载关系数据"""
        relations_path = os.path.join(self.path, "relations")
        
        # 用户-视频交互（核心数据）
        user_video_file = os.path.join(relations_path, "user-video.json")
        if not os.path.exists(user_video_file):
            # 尝试从additional_information加载
            user_video_file = os.path.join(self.path, "additional_information", "user_video_act.json")

        user_video_data = self._load_json_file(user_video_file)
        print(f"加载了 {len(user_video_data)} 条原始交互记录")

        # 分析数据格式
        if user_video_data and len(user_video_data) > 0:
            sample = user_video_data[0]
            print(f"数据样本: {sample}")

        # 处理用户-视频交互数据（制表符分隔格式）
        self.user_video_interactions = []
        valid_count = 0
        invalid_count = 0

        for i, interaction in enumerate(user_video_data):
            try:
                if isinstance(interaction, (list, tuple)) and len(interaction) >= 2:
                    # 制表符分隔格式: [user_id, video_id]
                    user_id = interaction[0].strip()
                    video_id = interaction[1].strip()
                    timestamp = 0

                    # 验证用户和视频是否存在
                    if user_id in self.user_info and video_id in self.video_info:
                        self.user_video_interactions.append({
                            'user_id': user_id,
                            'video_id': video_id,
                            'timestamp': timestamp
                        })
                        valid_count += 1
                    else:
                        invalid_count += 1
                        if i < 5:  # 只打印前5个无效样本
                            print(f"无效交互 {i}: user_id={user_id} (存在: {user_id in self.user_info}), "
                                  f"video_id={video_id} (存在: {video_id in self.video_info})")
                else:
                    invalid_count += 1
                    if i < 5:
                        print(f"格式错误 {i}: {interaction}")

            except Exception as e:
                invalid_count += 1
                if i < 5:
                    print(f"处理错误 {i}: {interaction}, 错误: {e}")

        print(f"有效交互: {valid_count}, 无效交互: {invalid_count}")
        
        # 视频-概念关系
        video_concept_data = self._load_json_file(os.path.join(relations_path, "video-concept.json"))
        self.video_concept_relations = video_concept_data

        # 课程-视频关系
        course_video_data = self._load_json_file(os.path.join(relations_path, "course-video.json"))
        self.course_video_relations = course_video_data

        # 先修依赖关系（可选）
        prerequisite_file = os.path.join(relations_path, "prerequisite-dependency.json")
        if not os.path.exists(prerequisite_file):
            prerequisite_file = os.path.join(relations_path, "prerequisite.json")
        prerequisite_data = self._load_json_file(prerequisite_file)
        self.prerequisite_relations = prerequisite_data

        # 教师-课程关系（可选）
        teacher_course_file = os.path.join(relations_path, "teacher-course.json")
        if not os.path.exists(teacher_course_file):
            teacher_course_file = os.path.join(relations_path, "video-teacher.json")
        teacher_course_data = self._load_json_file(teacher_course_file)
        self.teacher_course_relations = teacher_course_data
        
        cprint(f"Loaded relations: {len(self.user_video_interactions)} user-video interactions")
    
    def _apply_core_filtering(self):
        """应用10核过滤"""
        # 统计用户交互次数
        user_counts = defaultdict(int)
        video_counts = defaultdict(int)
        
        for interaction in self.user_video_interactions:
            user_counts[interaction['user_id']] += 1
            video_counts[interaction['video_id']] += 1
        
        # 过滤用户（>=10次交互）
        valid_users = {uid for uid, count in user_counts.items() if count >= self.min_interactions}
        
        # 过滤视频（>=5次观看）
        valid_videos = {vid for vid, count in video_counts.items() if count >= 5}
        
        # 过滤交互数据
        filtered_interactions = []
        for interaction in self.user_video_interactions:
            if (interaction['user_id'] in valid_users and 
                interaction['video_id'] in valid_videos):
                filtered_interactions.append(interaction)
        
        self.user_video_interactions = filtered_interactions
        
        # 重新映射ID
        self.user_id_map = {uid: idx for idx, uid in enumerate(sorted(valid_users))}
        self.video_id_map = {vid: idx for idx, vid in enumerate(sorted(valid_videos))}
        
        # 反向映射
        self.idx_to_user = {idx: uid for uid, idx in self.user_id_map.items()}
        self.idx_to_video = {idx: vid for vid, idx in self.video_id_map.items()}
        
        cprint(f"After {self.min_interactions}-core filtering: {len(valid_users)} users, "
               f"{len(valid_videos)} videos, {len(filtered_interactions)} interactions")
    
    def _build_knowledge_graph(self):
        """构建知识图谱"""
        print("构建知识图谱...")
        self.kg_relations = {
            'video_concept': defaultdict(list),
            'course_video': defaultdict(list),
            'teacher_course': defaultdict(list),
            'prerequisite': defaultdict(list)
        }

        # 视频-概念关系（制表符分隔格式）
        print(f"处理 {len(self.video_concept_relations)} 条视频-概念关系...")
        valid_video_ids = set(self.video_id_map.keys())  # 预先转换为集合提高查找效率

        for i, relation in enumerate(self.video_concept_relations):
            if i % 50000 == 0 and i > 0:
                print(f"  已处理 {i}/{len(self.video_concept_relations)} 条视频-概念关系")

            if isinstance(relation, (list, tuple)) and len(relation) >= 2:
                video_id = relation[0].strip()
                concept_id = relation[1].strip()
                if video_id in valid_video_ids:
                    self.kg_relations['video_concept'][video_id].append(concept_id)

        # 课程-视频关系（制表符分隔格式）
        print(f"处理 {len(self.course_video_relations)} 条课程-视频关系...")
        for i, relation in enumerate(self.course_video_relations):
            if i % 10000 == 0 and i > 0:
                print(f"  已处理 {i}/{len(self.course_video_relations)} 条课程-视频关系")

            if isinstance(relation, (list, tuple)) and len(relation) >= 2:
                course_id = relation[0].strip()
                video_id = relation[1].strip()
                if video_id in valid_video_ids:
                    self.kg_relations['course_video'][course_id].append(video_id)

        # 教师-课程关系（制表符分隔格式）
        print(f"处理 {len(self.teacher_course_relations)} 条教师-课程关系...")
        for relation in self.teacher_course_relations:
            if isinstance(relation, (list, tuple)) and len(relation) >= 2:
                teacher_id = relation[0].strip()
                course_id = relation[1].strip()
                self.kg_relations['teacher_course'][teacher_id].append(course_id)

        # 先修依赖关系（制表符分隔格式）
        print(f"处理 {len(self.prerequisite_relations)} 条先修依赖关系...")
        for relation in self.prerequisite_relations:
            if isinstance(relation, (list, tuple)) and len(relation) >= 2:
                pre_concept = relation[0].strip()
                post_concept = relation[1].strip()
                self.kg_relations['prerequisite'][pre_concept].append(post_concept)

        print("知识图谱构建完成")
    
    def _split_train_test(self):
        """划分训练测试集（优化版本）"""
        print("开始划分训练测试集...")

        # 转换为DataFrame并添加索引
        interactions_df = pd.DataFrame(self.user_video_interactions)
        interactions_df['user_idx'] = interactions_df['user_id'].map(self.user_id_map)
        interactions_df['video_idx'] = interactions_df['video_id'].map(self.video_id_map)

        print(f"处理 {len(interactions_df)} 条交互记录...")

        # 按用户分组，避免逐个用户处理
        train_data = []
        test_data = []

        # 按用户分组处理
        grouped = interactions_df.groupby('user_id')
        processed_users = 0
        total_users = len(self.user_id_map)

        for user_id, user_interactions in grouped:
            processed_users += 1
            if processed_users % 10000 == 0:
                print(f"  已处理 {processed_users}/{total_users} 个用户")

            # 按时间戳排序
            user_interactions = user_interactions.sort_values('timestamp')
            n_interactions = len(user_interactions)

            if n_interactions >= 5:  # 至少5个交互才能划分
                split_idx = int(n_interactions * 0.8)
                train_data.extend(user_interactions.iloc[:split_idx].to_dict('records'))
                test_data.extend(user_interactions.iloc[split_idx:].to_dict('records'))
            else:
                # 少于5个交互的用户全部放入训练集
                train_data.extend(user_interactions.to_dict('records'))

        self.train_data = train_data
        self.test_data = test_data

        print(f"训练测试集划分完成: {len(train_data)} 训练, {len(test_data)} 测试交互")
        
        # 构建训练数据数组
        self.trainUser = np.array([d['user_idx'] for d in train_data])
        self.trainItem = np.array([d['video_idx'] for d in train_data])
        self.trainUniqueUsers = np.unique(self.trainUser)
        
        # 构建测试数据字典
        self._testDict = defaultdict(list)
        for d in test_data:
            self._testDict[d['user_idx']].append(d['video_idx'])
        
        # 构建所有正向交互字典
        self._allPos = defaultdict(list)
        for d in train_data:
            self._allPos[d['user_idx']].append(d['video_idx'])
    
    def _save_to_cache(self, cache_file):
        """保存到缓存"""
        cache_data = {
            'user_id_map': self.user_id_map,
            'video_id_map': self.video_id_map,
            'idx_to_user': self.idx_to_user,
            'idx_to_video': self.idx_to_video,
            'train_data': self.train_data,
            'test_data': self.test_data,
            'kg_relations': dict(self.kg_relations),
            'user_info': self.user_info,
            'video_info': self.video_info,
            'concept_info': self.concept_info,
            'course_info': self.course_info,
            'teacher_info': self.teacher_info
        }
        
        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)
        cprint(f"Data cached to {cache_file}")
    
    def _load_from_cache(self, cache_file):
        """从缓存加载"""
        with open(cache_file, 'rb') as f:
            cache_data = pickle.load(f)
        
        self.user_id_map = cache_data['user_id_map']
        self.video_id_map = cache_data['video_id_map']
        self.idx_to_user = cache_data['idx_to_user']
        self.idx_to_video = cache_data['idx_to_video']
        self.train_data = cache_data['train_data']
        self.test_data = cache_data['test_data']
        self.kg_relations = defaultdict(list, cache_data['kg_relations'])
        self.user_info = cache_data['user_info']
        self.video_info = cache_data['video_info']
        self.concept_info = cache_data['concept_info']
        self.course_info = cache_data['course_info']
        self.teacher_info = cache_data['teacher_info']
        
        # 重建训练数据数组
        self.trainUser = np.array([d['user_idx'] for d in self.train_data])
        self.trainItem = np.array([d['video_idx'] for d in self.train_data])
        self.trainUniqueUsers = np.unique(self.trainUser)
        
        # 重建测试数据字典
        self._testDict = defaultdict(list)
        for d in self.test_data:
            self._testDict[d['user_idx']].append(d['video_idx'])
        
        # 重建所有正向交互字典
        self._allPos = defaultdict(list)
        for d in self.train_data:
            self._allPos[d['user_idx']].append(d['video_idx'])
    
    def _build_sparse_graph(self):
        """构建用户-视频二分图"""
        try:
            pre_adj_mat = sp.load_npz(os.path.join(self.cache_dir, 'adj_mat.npz'))
            cprint("Successfully loaded adjacency matrix.")
            norm_adj = pre_adj_mat
        except:
            cprint("Generating adjacency matrix...")
            s = time()
            adj_mat = sp.dok_matrix((self.n_users + self.m_items, self.n_users + self.m_items), dtype=np.float32)
            adj_mat = adj_mat.tolil()
            R = csr_matrix((np.ones(len(self.trainUser)), (self.trainUser, self.trainItem)), 
                          shape=(self.n_users, self.m_items))
            
            adj_mat[:self.n_users, self.n_users:] = R
            adj_mat[self.n_users:, :self.n_users] = R.T
            adj_mat = adj_mat.todok()
            
            rowsum = np.array(adj_mat.sum(axis=1))
            d_inv = np.power(rowsum, -0.5).flatten()
            d_inv[np.isinf(d_inv)] = 0.
            d_mat = sp.diags(d_inv)
            
            norm_adj = d_mat.dot(adj_mat)
            norm_adj = norm_adj.dot(d_mat)
            norm_adj = norm_adj.tocsr()
            end = time()
            cprint(f"Costing {end-s}s, saved norm_mat...")
            sp.save_npz(os.path.join(self.cache_dir, 'adj_mat.npz'), norm_adj)
        
        if world.config['A_split']:
            self.Graph = self._split_A_hat(norm_adj)
            cprint("Done split matrix")
        else:
            self.Graph = self._convert_sp_mat_to_sp_tensor(norm_adj)
            self.Graph = self.Graph.coalesce().to(world.device)
            cprint("Don't split the matrix")
    
    @property
    def n_users(self):
        return len(self.user_id_map)
    
    @property
    def m_items(self):
        return len(self.video_id_map)
    
    @property
    def trainDataSize(self):
        return len(self.train_data)
    
    @property
    def testDict(self):
        return self._testDict
    
    @property
    def allPos(self):
        return self._allPos
    
    def _convert_sp_mat_to_sp_tensor(self, X):
        coo = X.tocoo().astype(np.float32)
        row = torch.Tensor(coo.row).long()
        col = torch.Tensor(coo.col).long()
        index = torch.stack([row, col])
        data = torch.FloatTensor(coo.data)
        return torch.sparse.FloatTensor(index, data, torch.Size(coo.shape))
    
    def _split_A_hat(self, A):
        A_fold = []
        fold_len = (self.n_users + self.m_items) // world.config['A_n_fold']
        for i_fold in range(world.config['A_n_fold']):
            start = i_fold * fold_len
            if i_fold == world.config['A_n_fold'] - 1:
                end = self.n_users + self.m_items
            else:
                end = (i_fold + 1) * fold_len
            A_fold.append(self._convert_sp_mat_to_sp_tensor(A[start:end]).coalesce().to(world.device))
        return A_fold
    
    def getUserItemFeedback(self, users, items):
        return np.array(self.allPos)[users, items]
    
    def getUserPosItems(self, users):
        posItems = []
        for user in users:
            posItems.append(self.allPos[user])
        return posItems
    
    def getUserNegItems(self, users):
        negItems = []
        for user in users:
            negItems.append(self.negItems[user])
        return negItems
    
    def getSparseGraph(self):
        return self.Graph
    
    def __getitem__(self, index):
        user = self.trainUser[index]
        pos_item = self.trainItem[index]
        while True:
            neg_item = np.random.randint(0, self.m_items)
            if neg_item not in self.allPos[user]:
                break
        return user, pos_item, neg_item
    
    def __len__(self):
        return len(self.trainUser)
