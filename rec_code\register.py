import world
import dataloader
import model
import utils
from pprint import pprint

# 根据 world.dataset 的值（通过 parse.py 传入，例如 ml-1m）初始化数据加载器。
if world.dataset in ['ml-1m', 'mind', 'lastfm', 'fund']:
    # 使用 dataloader.Loader 类加载指定数据集
    dataset = dataloader.Loader(path="../data/"+world.dataset)

# 打印配置信息，便于调试和记录
print('===========config================')
pprint(world.config)
print("cores for test:", world.CORES)
print("comment:", world.comment)
print("tensorboard:", world.tensorboard)
print("LOAD:", world.LOAD)
print("Weight path:", world.PATH)
print("Test Topks:", world.topks)
print("using bpr loss")
print('===========end===================')
# 定义支持的模型类型及其对应类，供 main.py 使用。
MODELS = {
    'mf': model.PureMF,
    'lgn': model.LightGCN,
    'colakg': model.CoLaKG,
}