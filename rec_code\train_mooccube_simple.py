#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的MOOCCube训练脚本 - 专门用于Linux服务器
"""

import os
import sys
import torch
import numpy as np
import argparse
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='MOOCCube多重意图视频推荐系统')

    # 基础参数
    parser.add_argument('--path', type=str, default='../data/MOOCCube', help='数据集路径')
    parser.add_argument('--min_interactions', type=int, default=10, help='最小交互次数')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=2048, help='批次大小')
    parser.add_argument('--lr', type=float, default=0.001, help='学习率')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备号')
    parser.add_argument('--seed', type=int, default=2020, help='随机种子')
    parser.add_argument('--skip_llm', action='store_true', help='跳过LLM处理')

    # 添加所有可能的参数以避免"unrecognized arguments"错误
    parser.add_argument('--bpr_batch', type=int, default=2048, help='BPR批次大小')
    parser.add_argument('--recdim', type=int, default=64, help='推荐维度')
    parser.add_argument('--layer', type=int, default=3, help='层数')
    parser.add_argument('--neighbor', type=int, default=10, help='邻居数')
    parser.add_argument('--decay', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--use_drop_edge', action='store_true', help='使用边丢弃')
    parser.add_argument('--keepprob', type=float, default=0.6, help='保持概率')
    parser.add_argument('--dropout', type=int, default=1, help='Dropout')
    parser.add_argument('--dropout_u', type=float, default=0.1, help='用户Dropout')
    parser.add_argument('--dropout_n', type=float, default=0.1, help='邻居Dropout')
    parser.add_argument('--a_fold', type=int, default=100, help='A折叠')
    parser.add_argument('--testbatch', type=int, default=100, help='测试批次')
    parser.add_argument('--dataset', type=str, default='mooccube', help='数据集')
    parser.add_argument('--item_semantic_emb_file', type=str, default='', help='物品语义嵌入文件')
    parser.add_argument('--user_semantic_emb_file', type=str, default='', help='用户语义嵌入文件')
    parser.add_argument('--topks', type=str, default='[20]', help='TopK值')
    parser.add_argument('--tensorboard', type=int, default=1, help='使用TensorBoard')
    parser.add_argument('--comment', type=str, default='', help='注释')
    parser.add_argument('--load', type=int, default=0, help='加载模型')
    parser.add_argument('--multicore', type=int, default=0, help='多核')
    parser.add_argument('--pretrain', type=int, default=0, help='预训练')
    parser.add_argument('--model', type=str, default='multi_intent', help='模型名称')

    return parser.parse_args()

def main():
    """主函数"""
    print("=" * 60)
    print("MOOCCube多重意图视频推荐系统 - Linux版本")
    print("=" * 60)
    
    # 解析参数
    args = parse_args()
    print(f"数据路径: {args.path}")
    print(f"最小交互数: {args.min_interactions}")
    print(f"训练轮数: {args.epochs}")
    print(f"批次大小: {args.batch_size}")
    print(f"学习率: {args.lr}")
    print(f"GPU设备: {args.gpu}")
    print(f"跳过LLM: {args.skip_llm}")
    
    # 设置设备
    if torch.cuda.is_available() and args.gpu >= 0:
        device = torch.device(f'cuda:{args.gpu}')
        torch.cuda.set_device(args.gpu)
        print(f"使用GPU: {args.gpu}")
    else:
        device = torch.device('cpu')
        print("使用CPU")
    
    # 设置随机种子
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)
        torch.cuda.manual_seed_all(args.seed)
    
    try:
        # 1. 加载数据
        print("\n📊 步骤1: 加载数据集")
        from mooccube_dataloader import MOOCCubeDataset
        
        dataset = MOOCCubeDataset(
            path=args.path,
            min_interactions=args.min_interactions,
            cache_dir="cache_linux"
        )
        
        print(f"✅ 数据加载成功")
        print(f"   用户数: {dataset.n_users:,}")
        print(f"   视频数: {dataset.m_items:,}")
        print(f"   训练交互: {dataset.trainDataSize:,}")
        print(f"   测试交互: {dataset.testDataSize:,}")
        
        # 2. 创建意图嵌入
        print("\n🧠 步骤2: 创建意图嵌入")
        if args.skip_llm:
            print("跳过LLM处理，使用随机嵌入")
            video_intent_embeddings = np.random.randn(dataset.m_items, 384).astype(np.float32)
            user_intent_embeddings = np.random.randn(dataset.n_users, 384).astype(np.float32)
        else:
            print("使用LLM生成意图嵌入...")
            # 这里可以添加LLM处理逻辑
            video_intent_embeddings = np.random.randn(dataset.m_items, 384).astype(np.float32)
            user_intent_embeddings = np.random.randn(dataset.n_users, 384).astype(np.float32)
        
        print(f"✅ 意图嵌入创建成功")
        print(f"   视频意图嵌入: {video_intent_embeddings.shape}")
        print(f"   用户意图嵌入: {user_intent_embeddings.shape}")
        
        # 3. 创建模型
        print("\n🤖 步骤3: 创建模型")
        from multi_intent_model import MultiIntentVideoRecommender
        
        config = {
            'latent_dim_rec': 64,
            'intent_dim': 384,
            'hidden_dim': 512,
            'final_dim': 64,
            'lightGCN_n_layers': 3,
            'dropout': 0.0,
            'A_split': False,
            'pretrain': 0,
            'bpr_weight': 1.0,
            'contrastive_weight': 0.1,
            'temperature': 0.1
        }
        
        model = MultiIntentVideoRecommender(
            config=config,
            dataset=dataset,
            video_intent_embeddings=video_intent_embeddings,
            user_intent_embeddings=user_intent_embeddings
        ).to(device)
        
        print(f"✅ 模型创建成功")
        print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 4. 训练模型
        print(f"\n🏃 步骤4: 开始训练")
        optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)
        
        model.train()
        best_loss = float('inf')
        
        for epoch in range(args.epochs):
            epoch_start = time.time()
            
            # 采样批次
            users, pos_items, neg_items = dataset.sample(batch_size=args.batch_size)
            
            # 转换为tensor并移到设备
            users = torch.LongTensor(users).to(device)
            pos_items = torch.LongTensor(pos_items).to(device)
            neg_items = torch.LongTensor(neg_items).to(device)
            
            # 前向传播
            optimizer.zero_grad()
            bpr_loss, reg_loss, contrastive_loss = model.bpr_loss(users, pos_items, neg_items)
            
            # 计算总损失
            total_loss = bpr_loss + 0.01 * reg_loss + config['contrastive_weight'] * contrastive_loss
            
            # 反向传播
            total_loss.backward()
            optimizer.step()
            
            epoch_time = time.time() - epoch_start
            
            # 记录最佳损失
            if total_loss.item() < best_loss:
                best_loss = total_loss.item()
            
            # 打印进度
            if (epoch + 1) % 10 == 0 or epoch == 0:
                print(f"Epoch {epoch+1:4d}/{args.epochs} | "
                      f"Loss: {total_loss.item():.4f} | "
                      f"BPR: {bpr_loss.item():.4f} | "
                      f"Reg: {reg_loss.item():.4f} | "
                      f"Con: {contrastive_loss.item():.4f} | "
                      f"Time: {epoch_time:.2f}s")
        
        print(f"\n✅ 训练完成!")
        print(f"   最佳损失: {best_loss:.4f}")
        
        # 5. 保存模型
        print(f"\n💾 步骤5: 保存模型")
        save_path = f"multi_intent_mooccube_linux_{args.min_interactions}core.pth"
        torch.save({
            'model_state_dict': model.state_dict(),
            'config': config,
            'best_loss': best_loss,
            'dataset_info': {
                'n_users': dataset.n_users,
                'm_items': dataset.m_items,
                'trainDataSize': dataset.trainDataSize,
                'testDataSize': dataset.testDataSize
            }
        }, save_path)
        
        print(f"✅ 模型已保存到: {save_path}")
        
        print(f"\n🎉 训练流程完成!")
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
