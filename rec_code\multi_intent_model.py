import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple
try:
    import world
    from model import LightGCN
except ImportError:
    # 如果导入失败，创建临时的world模块
    class World:
        def __init__(self):
            self.config = {}
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    world = World()

    # 简化的LightGCN实现
    class LightGCN(nn.Module):
        def __init__(self, config, dataset):
            super(LightGCN, self).__init__()
            self.num_users = dataset.n_users
            self.num_items = dataset.m_items
            self.latent_dim = config['latent_dim_rec']
            self.n_layers = config['lightGCN_n_layers']
            self.Graph = dataset.getSparseGraph()

            self.embedding_user = nn.Embedding(self.num_users, self.latent_dim)
            self.embedding_item = nn.Embedding(self.num_items, self.latent_dim)

            nn.init.normal_(self.embedding_user.weight, std=0.1)
            nn.init.normal_(self.embedding_item.weight, std=0.1)

        def computer(self):
            users_emb = self.embedding_user.weight
            items_emb = self.embedding_item.weight
            all_emb = torch.cat([users_emb, items_emb])

            embs = [all_emb]
            for layer in range(self.n_layers):
                all_emb = torch.sparse.mm(self.Graph, all_emb)
                embs.append(all_emb)

            embs = torch.stack(embs, dim=1)
            light_out = torch.mean(embs, dim=1)
            users, items = torch.split(light_out, [self.num_users, self.num_items])

            return users, items

class MultiIntentVideoRecommender(nn.Module):
    """
    多重意图视频推荐模型
    结合协同过滤和意图语义理解的推荐系统
    """
    
    def __init__(self, config, dataset, video_intent_embeddings, user_intent_embeddings):
        super(MultiIntentVideoRecommender, self).__init__()
        
        self.config = config
        self.dataset = dataset
        self.num_users = dataset.n_users
        self.num_items = dataset.m_items
        
        # 嵌入维度配置
        self.latent_dim = config['latent_dim_rec']  # 协同过滤嵌入维度
        self.intent_dim = config.get('intent_dim', 384)  # 意图嵌入维度
        self.hidden_dim = config.get('hidden_dim', 512)  # 隐藏层维度
        self.final_dim = config.get('final_dim', 64)  # 最终嵌入维度
        
        # 温度参数
        self.temperature = config.get('temperature', 0.1)
        
        # 协同过滤模块 - 使用LightGCN
        self.lightgcn = LightGCN(config, dataset)
        
        # 意图嵌入预处理
        self._init_intent_embeddings(video_intent_embeddings, user_intent_embeddings)
        
        # 维度缩减网络
        self.video_intent_projector = nn.Sequential(
            nn.Linear(self.intent_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(self.hidden_dim, self.final_dim),
            nn.LayerNorm(self.final_dim)
        )
        
        self.user_intent_projector = nn.Sequential(
            nn.Linear(self.intent_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(self.hidden_dim, self.final_dim),
            nn.LayerNorm(self.final_dim)
        )
        
        # 协同嵌入投影网络
        self.collab_projector = nn.Sequential(
            nn.Linear(self.latent_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(self.hidden_dim, self.final_dim),
            nn.LayerNorm(self.final_dim)
        )
        
        # 融合权重
        self.fusion_weight = nn.Parameter(torch.tensor(0.5))
        
        print(f"MultiIntentVideoRecommender initialized:")
        print(f"  Users: {self.num_users}, Videos: {self.num_items}")
        print(f"  Intent dim: {self.intent_dim}, Final dim: {self.final_dim}")
        print(f"  Video intent embeddings: {self.video_intent_emb.shape}")
        print(f"  User intent embeddings: {self.user_intent_emb.shape}")
    
    def _init_intent_embeddings(self, video_intent_embeddings, user_intent_embeddings):
        """初始化意图嵌入"""
        # 初始化视频意图嵌入矩阵
        video_intent_matrix = torch.zeros(self.num_items, self.intent_dim)
        for video_idx in range(self.num_items):
            video_id = self.dataset.idx_to_video.get(video_idx)
            if video_id and video_id in video_intent_embeddings:
                video_intent_matrix[video_idx] = torch.tensor(video_intent_embeddings[video_id])
            else:
                # 使用随机初始化
                video_intent_matrix[video_idx] = torch.randn(self.intent_dim) * 0.1
        
        # 初始化用户意图嵌入矩阵
        user_intent_matrix = torch.zeros(self.num_users, self.intent_dim)
        for user_idx in range(self.num_users):
            if user_idx in user_intent_embeddings:
                user_intent_matrix[user_idx] = torch.tensor(user_intent_embeddings[user_idx])
            else:
                # 使用随机初始化
                user_intent_matrix[user_idx] = torch.randn(self.intent_dim) * 0.1
        
        # 注册为缓冲区（不参与梯度更新）
        self.register_buffer('video_intent_emb', video_intent_matrix)
        self.register_buffer('user_intent_emb', user_intent_matrix)
    
    def get_collaborative_embeddings(self, users, items):
        """获取协同过滤嵌入"""
        all_users, all_items = self.lightgcn.computer()
        
        users_emb = all_users[users]
        items_emb = all_items[items]
        
        return users_emb, items_emb
    
    def get_intent_embeddings(self, users, items):
        """获取意图嵌入"""
        user_intent = self.user_intent_emb[users]
        item_intent = self.video_intent_emb[items]
        
        # 通过投影网络降维
        user_intent_proj = self.user_intent_projector(user_intent)
        item_intent_proj = self.video_intent_projector(item_intent)
        
        return user_intent_proj, item_intent_proj
    
    def contrastive_fusion(self, collab_emb, intent_emb, indices=None):
        """
        对比学习融合协同嵌入和意图嵌入
        
        Args:
            collab_emb: 协同过滤嵌入 [batch_size, dim]
            intent_emb: 意图嵌入 [batch_size, dim]
            indices: 实体索引 [batch_size]
            
        Returns:
            融合后的嵌入和对比损失
        """
        # 投影到相同维度
        collab_proj = self.collab_projector(collab_emb)
        
        # L2归一化
        collab_proj = F.normalize(collab_proj, dim=1)
        intent_emb = F.normalize(intent_emb, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(collab_proj, intent_emb.T) / self.temperature
        
        # 对比学习损失 (InfoNCE)
        batch_size = collab_proj.size(0)
        labels = torch.arange(batch_size).to(collab_proj.device)
        
        contrastive_loss = F.cross_entropy(similarity_matrix, labels)
        
        # 融合嵌入
        alpha = torch.sigmoid(self.fusion_weight)
        fused_emb = alpha * collab_proj + (1 - alpha) * intent_emb
        
        return fused_emb, contrastive_loss
    
    def forward(self, users, pos_items, neg_items):
        """
        前向传播
        
        Args:
            users: 用户索引 [batch_size]
            pos_items: 正样本项目索引 [batch_size]
            neg_items: 负样本项目索引 [batch_size]
            
        Returns:
            用户嵌入、正样本嵌入、负样本嵌入、对比损失
        """
        # 获取协同过滤嵌入
        users_collab, pos_items_collab = self.get_collaborative_embeddings(users, pos_items)
        _, neg_items_collab = self.get_collaborative_embeddings(users, neg_items)
        
        # 获取意图嵌入
        users_intent, pos_items_intent = self.get_intent_embeddings(users, pos_items)
        _, neg_items_intent = self.get_intent_embeddings(users, neg_items)
        
        # 对比融合
        users_fused, user_contrastive_loss = self.contrastive_fusion(
            users_collab, users_intent, users
        )
        
        pos_items_fused, pos_contrastive_loss = self.contrastive_fusion(
            pos_items_collab, pos_items_intent, pos_items
        )
        
        neg_items_fused, neg_contrastive_loss = self.contrastive_fusion(
            neg_items_collab, neg_items_intent, neg_items
        )
        
        # 总对比损失
        total_contrastive_loss = (user_contrastive_loss + pos_contrastive_loss + neg_contrastive_loss) / 3
        
        return users_fused, pos_items_fused, neg_items_fused, total_contrastive_loss
    
    def predict(self, users, items):
        """
        预测用户对项目的评分
        
        Args:
            users: 用户索引
            items: 项目索引
            
        Returns:
            预测评分
        """
        # 获取协同过滤嵌入
        users_collab, items_collab = self.get_collaborative_embeddings(users, items)
        
        # 获取意图嵌入
        users_intent, items_intent = self.get_intent_embeddings(users, items)
        
        # 融合嵌入（不计算对比损失）
        users_collab_proj = self.collab_projector(users_collab)
        items_collab_proj = self.collab_projector(items_collab)
        
        users_collab_proj = F.normalize(users_collab_proj, dim=1)
        items_collab_proj = F.normalize(items_collab_proj, dim=1)
        users_intent = F.normalize(users_intent, dim=1)
        items_intent = F.normalize(items_intent, dim=1)
        
        # 融合
        alpha = torch.sigmoid(self.fusion_weight)
        users_fused = alpha * users_collab_proj + (1 - alpha) * users_intent
        items_fused = alpha * items_collab_proj + (1 - alpha) * items_intent
        
        # 计算评分
        scores = torch.sum(users_fused * items_fused, dim=1)
        
        return scores
    
    def get_all_ratings(self, users):
        """
        获取用户对所有项目的评分
        
        Args:
            users: 用户索引列表
            
        Returns:
            评分矩阵 [len(users), num_items]
        """
        all_items = torch.arange(self.num_items).to(users.device)
        
        # 扩展用户和项目索引
        users_expanded = users.unsqueeze(1).expand(-1, self.num_items).reshape(-1)
        items_expanded = all_items.unsqueeze(0).expand(len(users), -1).reshape(-1)
        
        # 批量预测
        batch_size = 10000  # 避免内存溢出
        all_scores = []
        
        for i in range(0, len(users_expanded), batch_size):
            batch_users = users_expanded[i:i+batch_size]
            batch_items = items_expanded[i:i+batch_size]
            
            batch_scores = self.predict(batch_users, batch_items)
            all_scores.append(batch_scores)
        
        # 重塑为矩阵形式
        all_scores = torch.cat(all_scores, dim=0)
        rating_matrix = all_scores.reshape(len(users), self.num_items)
        
        return rating_matrix
    
    def bpr_loss(self, users, pos_items, neg_items):
        """
        BPR损失函数
        """
        users_emb, pos_emb, neg_emb, contrastive_loss = self.forward(users, pos_items, neg_items)
        
        pos_scores = torch.sum(users_emb * pos_emb, dim=1)
        neg_scores = torch.sum(users_emb * neg_emb, dim=1)
        
        bpr_loss = -torch.mean(torch.log(torch.sigmoid(pos_scores - neg_scores) + 1e-8))
        
        # 正则化损失
        reg_loss = (1/2) * (users_emb.norm(2).pow(2) + 
                           pos_emb.norm(2).pow(2) + 
                           neg_emb.norm(2).pow(2)) / float(len(users))
        
        return bpr_loss, reg_loss, contrastive_loss


class MultiIntentTrainer:
    """
    多重意图推荐模型训练器
    """
    
    def __init__(self, model, dataset, config):
        self.model = model
        self.dataset = dataset
        self.config = config
        
        # 优化器
        self.optimizer = torch.optim.Adam(model.parameters(), lr=config['lr'])
        
        # 损失权重
        self.bpr_weight = config.get('bpr_weight', 1.0)
        self.reg_weight = config.get('decay', 1e-4)
        self.contrastive_weight = config.get('contrastive_weight', 0.1)
        
        print(f"Trainer initialized with lr={config['lr']}")
        print(f"Loss weights: BPR={self.bpr_weight}, Reg={self.reg_weight}, Contrastive={self.contrastive_weight}")
    
    def train_one_epoch(self, dataloader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        total_bpr_loss = 0
        total_reg_loss = 0
        total_contrastive_loss = 0
        
        for batch_idx, (users, pos_items, neg_items) in enumerate(dataloader):
            users = users.to(world.device)
            pos_items = pos_items.to(world.device)
            neg_items = neg_items.to(world.device)
            
            # 前向传播
            bpr_loss, reg_loss, contrastive_loss = self.model.bpr_loss(users, pos_items, neg_items)
            
            # 总损失
            loss = (self.bpr_weight * bpr_loss + 
                   self.reg_weight * reg_loss + 
                   self.contrastive_weight * contrastive_loss)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            # 累计损失
            total_loss += loss.item()
            total_bpr_loss += bpr_loss.item()
            total_reg_loss += reg_loss.item()
            total_contrastive_loss += contrastive_loss.item()
            
            if batch_idx % 100 == 0:
                print(f"Batch {batch_idx}: Loss={loss.item():.4f}, "
                      f"BPR={bpr_loss.item():.4f}, "
                      f"Reg={reg_loss.item():.4f}, "
                      f"Contrastive={contrastive_loss.item():.4f}")
        
        avg_loss = total_loss / len(dataloader)
        avg_bpr = total_bpr_loss / len(dataloader)
        avg_reg = total_reg_loss / len(dataloader)
        avg_contrastive = total_contrastive_loss / len(dataloader)
        
        return avg_loss, avg_bpr, avg_reg, avg_contrastive
